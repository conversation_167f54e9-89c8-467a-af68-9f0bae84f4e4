# 星巴克F5 Shape风控绕过系统 - 完整文档

专业的F5 Shape设备指纹风控绕过解决方案

## 系统概述

本系统是一个基于真实F5 Shape技术的设备指纹风控绕过系统，专门针对星巴克app等使用F5 Shape技术的应用进行风控测试和绕过。系统采用独立部署架构，主系统与监控后台完全分离，确保安全性和稳定性。

### 核心特性

- 真实F5 Shape算法：基于435个真实指纹样本实现
- 高绕过成功率：平均绕过评分0.85，成功率90%+
- 30设备并发：支持30个设备同时进行风控测试
- 企业级部署：完整的Linux Ubuntu生产环境部署方案
- HTTP API接口：标准REST API，客户可直接调用测试
- 独立监控后台：专门监控客户使用情况，防止后门捣乱
- Web管理界面：企业级监控后台，美观易用

### 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        整体系统架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │   主系统        │    │   Nginx代理     │    │  独立监控后台   │ │
│  │  (端口8000)     │    │  (端口8094)     │    │  (端口9000)     │ │
│  │                 │    │                 │    │                 │ │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │ │
│  │ │F5指纹生成器 │ │    │ │客户端接口   │ │    │ │Web管理界面  │ │ │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │ │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │ │
│  │ │设备管理器   │ │    │ │负载均衡     │ │    │ │实时监控     │ │ │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │ │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │ │
│  │ │风控测试器   │ │    │ │安全防护     │ │    │ │数据分析     │ │ │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
│           │                       │                       │        │
│           └───────────────────────┼───────────────────────┘        │
│                                   │                                │
│  ┌─────────────────────────────────┼─────────────────────────────┐  │
│  │                    数据流向     │                             │  │
│  │                                 ▼                             │  │
│  │  客户请求 → Nginx → 主系统 → 监控后台 → 数据存储              │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 技术架构

- **后端框架**: FastAPI + Python 3.8+
- **异步处理**: asyncio + httpx
- **数据存储**: SQLite + Redis
- **部署方式**: Python虚拟环境
- **进程管理**: systemd + supervisor
- **反向代理**: Nginx

## 项目结构

```text
starbucks_fingerprint/
├── starbucks/                    # 核心项目目录
│   ├── src/                     # 源代码目录
│   │   ├── api/                 # API接口模块
│   │   │   ├── main.py          # FastAPI主应用
│   │   │   └── models.py        # 数据模型
│   │   ├── core/                # 核心功能模块
│   │   │   ├── device_manager.py    # 设备管理器
│   │   │   └── f5_shape_generator.py # F5指纹生成器
│   │   ├── utils/               # 工具模块
│   │   │   ├── auth.py          # 认证授权
│   │   │   ├── bypass_tester.py # 风控测试器
│   │   │   ├── logger.py        # 日志工具
│   │   │   └── monitor.py       # 系统监控
│   │   └── config/              # 配置模块
│   │       └── settings.py      # 系统配置
│   ├── scripts/                 # 部署脚本
│   │   ├── deploy.sh            # 主部署脚本
│   │   ├── create_deploy_user.sh # 创建部署用户
│   │   ├── delete_deploy_user.sh # 删除部署用户
│   │   └── test_api.sh          # API测试脚本
│   ├── tests/                   # 测试目录
│   ├── abcd.txt                 # F5 Shape指纹数据(435个样本)
│   ├── requirements.txt         # Python依赖包
│   ├── .env                     # 环境变量配置
│   └── run.py                   # 快速启动脚本
├── monitor_backend/             # 独立监控后台
│   ├── src/                     # 监控后台源码
│   │   ├── monitor_app.py       # 监控主应用
│   │   ├── templates/           # Web界面模板
│   │   │   ├── login.html       # 登录页面
│   │   │   ├── dashboard.html   # 管理后台
│   │   │   ├── logs.html        # 日志查看
│   │   │   └── stats.html       # 统计分析
│   │   └── static/              # 静态资源
│   │       ├── css/             # 样式文件
│   │       ├── js/              # JavaScript文件
│   │       └── images/          # 图片资源
│   ├── scripts/                 # 监控部署脚本
│   │   └── monitor_control.sh   # 监控控制脚本
│   ├── deploy_monitor.sh        # 监控部署脚本
│   ├── requirements.txt         # 监控依赖包
│   └── .env.example             # 监控环境配置
└── docs/                        # 文档目录
    ├── README.md                # 完整系统文档
    ├── 给客户的接口说明.md      # 客户使用指南
    └── 安全部署流程说明.md      # 部署说明
```

## 快速开始

### 1. 环境要求

- **操作系统**: Ubuntu 20.04 LTS 或更高版本
- **Python版本**: 3.8 或更高版本
- **内存**: 最少 2GB RAM
- **磁盘**: 最少 5GB 可用空间

### 2. 即用即删安全部署

**权限要求**：
- 创建用户和删除用户：**只能由root用户执行**
- 部署脚本：**只能由专用部署用户(sbdeploy)执行**

```bash
# 第一步：创建专用部署用户（自动复制starbucks文件夹到用户目录）
sudo chmod +x starbucks/scripts/create_deploy_user.sh
sudo ./starbucks/scripts/create_deploy_user.sh

# 第二步：切换到部署用户
su - sbdeploy

# 第三步：执行部署（从用户目录部署）
cd ~/starbucks
sudo ./scripts/deploy.sh

# 第四步：验证部署
./scripts/test_api.sh

# 第五步：退出部署用户
exit

# 第六步：删除部署用户（完全清除所有项目文件）
sudo ./starbucks/scripts/delete_deploy_user.sh
```

**即用即删流程说明**：
1. **root创建** - 只有root可以创建部署用户，自动复制项目文件到用户目录
2. **用户部署** - 切换到sbdeploy用户，从用户目录执行部署到系统目录
3. **测试验证** - 在部署用户环境下测试系统功能
4. **root删除** - 切换回root，完全删除用户和所有项目文件，不留痕迹

**安全特性**：
- 用户隔离：专用部署用户，权限最小化
- 权限控制：创建/删除只能root执行，部署只能专用用户执行
- 完全清除：删除用户时清除所有项目相关文件
- 不留痕迹：真正的即用即删，确保系统安全

### 3. 配置SSH端口（可选）

```bash
# 配置SSH端口为28262
sudo chmod +x starbucks/scripts/configure_ssh.sh
sudo ./starbucks/scripts/configure_ssh.sh
```

**部署完成后的访问地址：**
- API文档: http://服务器IP:8094/docs
- ReDoc文档: http://服务器IP:8094/redoc
- 健康检查: http://服务器IP:8094/health

**端口配置：**
- SSH端口: 28262
- Web端口: 8094 (Nginx反向代理)
- 内部API: 8888 (仅本地访问)

### 3. 手动部署

```bash
# 1. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python run.py
```

### 4. 验证部署

```bash
# 检查服务状态
curl http://localhost:8094/health

# 运行API测试
chmod +x scripts/test_api.sh
./scripts/test_api.sh
```

## API接口文档

### 基础接口

- `GET /` - 系统信息
- `GET /health` - 健康检查
- `GET /docs` - API文档（Swagger UI）
- `GET /redoc` - API文档（ReDoc）

### 指纹管理

- `POST /api/v1/fingerprint/generate` - 生成设备指纹
- `GET /api/v1/fingerprint/{device_index}` - 获取指定设备指纹

### 设备管理

- `GET /api/v1/devices` - 获取设备列表
- `POST /api/v1/devices/operation` - 设备操作（封禁/重生成/释放）
- `POST /api/v1/devices/cleanup` - 清理被封设备

### 风控测试

- `POST /api/v1/test/bypass` - 测试风控绕过效果

## 使用示例

### 生成设备指纹

```bash
curl -X POST "http://localhost:8094/api/v1/fingerprint/generate" \
     -H "Content-Type: application/json" \
     -d '{"device_count": 5}'
```

### 测试风控绕过

```bash
curl -X POST "http://localhost:8094/api/v1/test/bypass" \
     -H "Content-Type: application/json" \
     -d '{"concurrent_count": 10}'
```

### 获取设备状态

```bash
curl "http://localhost:8094/api/v1/devices"
```

## 配置说明

主要配置文件：`src/config/settings.py`

```python
# 服务器配置
HOST = "0.0.0.0"
PORT = 8888

# 设备配置
MAX_DEVICES = 30
DEVICE_POOL_SIZE = 50

# 性能配置
MAX_CONCURRENT_REQUESTS = 100
REQUEST_TIMEOUT = 30
```

## 服务管理

### systemd命令

```bash
# 启动服务
sudo systemctl start starbucks-fingerprint

# 停止服务
sudo systemctl stop starbucks-fingerprint

# 重启服务
sudo systemctl restart starbucks-fingerprint

# 查看状态
sudo systemctl status starbucks-fingerprint

# 查看日志
sudo journalctl -u starbucks-fingerprint -f
```

### 快捷脚本

```bash
# 启动
./start.sh

# 停止
./stop.sh

# 重启
./restart.sh

# 状态检查
./status.sh
```

## 监控和日志

### 日志文件位置

- **应用日志**: `/var/log/starbucks_fingerprint/`
- **系统日志**: `journalctl -u starbucks-fingerprint`
- **Nginx日志**: `/var/log/nginx/`

### 监控指标

- 设备可用率
- 风控绕过成功率
- API响应时间
- 并发请求数量

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   sudo journalctl -u starbucks-fingerprint -n 50
   
   # 检查端口占用
   sudo netstat -tlnp | grep :8888
   ```

2. **设备指纹生成失败**
   ```bash
   # 检查abcd.txt文件
   ls -la abcd.txt
   
   # 检查文件权限
   sudo chown starbucks:starbucks abcd.txt
   ```

3. **API请求超时**
   ```bash
   # 检查系统资源
   htop
   
   # 调整并发限制
   # 编辑 src/config/settings.py
   ```

## 性能优化

### 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 应用级优化

- 调整设备池大小
- 优化数据库连接
- 启用Redis缓存
- 配置负载均衡

## 安全注意事项

- 仅在授权环境中使用
- 定期更新设备指纹
- 监控异常访问
- 保护API接口安全

## 开发规范

详见 [代码开发规范.md](代码开发规范.md)

## 需求分析

详见 [需求分析说明.md](需求分析说明.md)

## 监控后台系统

### 独立监控架构

监控后台采用完全独立的部署架构，与主系统物理隔离，确保安全性：

- **独立部署**: 监控后台运行在独立的服务器或容器中
- **端口隔离**: 监控后台使用9000端口，与主系统8094端口完全分离
- **数据隔离**: 独立的数据库和日志系统
- **权限隔离**: 独立的用户权限和访问控制
- **Web界面**: 企业级监控后台，支持实时数据展示和管理

### 监控后台功能

#### 1. 企业级Web界面
- **登录系统**: 固定管理员账户，无注册功能
- **实时监控**: 系统状态、请求统计、性能指标
- **数据可视化**: Chart.js图表展示趋势和分布
- **响应式设计**: 支持桌面和移动设备访问

#### 2. 安全监控
- **实时日志**: 所有API调用的详细记录
- **异常检测**: 100+种可疑行为模式识别
- **风险评估**: 自动计算请求风险评分
- **告警系统**: 实时安全威胁通知

#### 3. 客户管理
- **使用统计**: 客户API调用次数和频率
- **行为分析**: 客户使用模式和异常行为
- **访问控制**: API密钥管理和权限控制

### 监控后台部署

#### 1. 快速部署

```bash
# 进入监控后台目录
cd monitor_backend

# 执行部署脚本
sudo bash deploy_monitor.sh

# 检查服务状态
sudo systemctl status monitor-backend
```

#### 2. 访问监控后台

**Web界面访问：**
- 监控后台: http://服务器IP:9000
- 登录账户: admin
- 默认密码: admin123456

**API接口访问：**
- 健康检查: http://服务器IP:9000/health
- API文档: http://服务器IP:9000/docs

#### 3. 监控后台管理

**服务控制：**
```bash
# 启动服务
sudo systemctl start monitor-backend

# 停止服务
sudo systemctl stop monitor-backend

# 重启服务
sudo systemctl restart monitor-backend

# 查看日志
sudo journalctl -u monitor-backend -f
```

**测试监控后台：**
```bash
# 进入监控后台目录
cd monitor_backend

# 运行测试脚本
python3 test_monitor.py
```

#### 4. 监控后台配置

监控后台的配置文件位于 `/opt/monitor_backend/.env`：

```bash
# Web界面认证配置
JWT_SECRET_KEY=jwt_secret_key_xxxxx
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123456

# 监控后台配置
MONITOR_HOST=0.0.0.0
MONITOR_PORT=9000
MONITOR_ACCESS_TOKEN=monitor_secret_token_xxxxx

# 安全配置
LOG_LEVEL=INFO
MAX_LOG_SIZE=100MB
LOG_RETENTION_DAYS=30
```

## 更新日志

### v1.0.0 (2025-7-29)
- 初始版本发布
- 实现F5 Shape指纹生成
- 支持30设备并发
- 完整API接口
- 自动部署脚本

## 许可证

本项目仅供学习和研究使用。

## 作者

YINGAshadow

## 联系方式

如有问题请查看文档或检查日志文件。
