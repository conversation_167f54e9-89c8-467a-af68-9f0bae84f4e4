#!/bin/bash

# 创建部署用户脚本
# 作者: YINGAshadow
# 用途: 创建固定的部署用户，用于安全部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 固定用户配置
DEPLOY_USER="sbdeploy"
DEPLOY_PASSWORD="SB2025Deploy#888"
DEPLOY_HOME="/home/<USER>"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "创建部署用户只能由root用户执行"
        log_error "当前用户: $(whoami)"
        echo ""
        echo "正确的执行方式："
        echo "  sudo ./starbucks/scripts/create_deploy_user.sh"
        echo ""
        exit 1
    fi
}

# 检查用户是否已存在
check_user_exists() {
    if id "$DEPLOY_USER" &>/dev/null; then
        log_warn "用户 $DEPLOY_USER 已存在"
        read -p "是否删除现有用户并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            delete_user_completely
        else
            log_info "使用现有用户 $DEPLOY_USER"
            return 0
        fi
    fi
}

# 创建部署用户
create_deploy_user() {
    log_step "创建部署用户 $DEPLOY_USER"
    
    # 创建用户
    useradd -m -s /bin/bash "$DEPLOY_USER"
    
    # 设置密码
    echo "$DEPLOY_USER:$DEPLOY_PASSWORD" | chpasswd
    
    # 添加到sudo组
    usermod -aG sudo "$DEPLOY_USER"
    
    # 配置sudo免密码（仅限部署相关命令）
    cat > /etc/sudoers.d/$DEPLOY_USER << EOF
# 部署用户sudo配置
$DEPLOY_USER ALL=(ALL) NOPASSWD: /bin/systemctl, /usr/bin/systemctl
$DEPLOY_USER ALL=(ALL) NOPASSWD: /usr/sbin/ufw, /bin/ufw
$DEPLOY_USER ALL=(ALL) NOPASSWD: /usr/bin/apt, /bin/apt
$DEPLOY_USER ALL=(ALL) NOPASSWD: /usr/bin/nginx, /bin/nginx
$DEPLOY_USER ALL=(ALL) NOPASSWD: /bin/mkdir, /bin/chown, /bin/chmod
$DEPLOY_USER ALL=(ALL) NOPASSWD: /bin/ln, /bin/rm
EOF
    
    log_info "用户 $DEPLOY_USER 创建完成"
}

# 设置用户环境
setup_user_environment() {
    log_step "配置用户环境"
    
    # 创建必要目录
    mkdir -p "$DEPLOY_HOME/.ssh"
    mkdir -p "$DEPLOY_HOME/logs"
    
    # 设置bashrc
    cat >> "$DEPLOY_HOME/.bashrc" << EOF

# 部署用户环境配置
export DEPLOY_USER="$DEPLOY_USER"
export PROJECT_NAME="starbucks-bypass"
export DEPLOY_TIME="\$(date '+%Y-%m-%d %H:%M:%S')"

# 别名
alias ll='ls -la'
alias logs='tail -f ~/logs/deploy.log'
alias status='systemctl status starbucks-bypass'

# 提示符
PS1='\[\033[01;32m\][\u@\h \W]\$\[\033[00m\] '

echo "=========================================="
echo "星巴克F5 Shape绕过系统部署用户"
echo "用户: $DEPLOY_USER"
echo "登录时间: \$DEPLOY_TIME"
echo "=========================================="
echo ""
echo "可用命令:"
echo "  部署系统: cd /opt/starbucks-bypass && sudo ./scripts/deploy.sh"
echo "  查看状态: status"
echo "  查看日志: logs"
echo ""
EOF
    
    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_HOME"
    chmod 700 "$DEPLOY_HOME/.ssh"
    
    log_info "用户环境配置完成"
}

# 复制项目文件到用户目录
copy_project_files() {
    log_step "复制项目文件到用户目录"

    # 创建用户项目目录
    USER_PROJECT_DIR="$DEPLOY_HOME/starbucks"
    mkdir -p "$USER_PROJECT_DIR"

    # 确定项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT=""

    log_info "脚本位置: $SCRIPT_DIR"

    # 从脚本位置向上查找项目根目录
    if [[ -f "$SCRIPT_DIR/../src/api/main.py" ]]; then
        # 脚本在 starbucks/scripts/ 目录下
        PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
        log_info "检测到项目根目录(模式1): $PROJECT_ROOT"
    elif [[ -f "$SCRIPT_DIR/../../starbucks/src/api/main.py" ]]; then
        # 脚本在项目根目录的某个子目录下
        PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")/starbucks"
        log_info "检测到项目根目录(模式2): $PROJECT_ROOT"
    elif [[ -f "$SCRIPT_DIR/../../../starbucks/src/api/main.py" ]]; then
        # 脚本在更深的目录下
        PROJECT_ROOT="$(dirname "$(dirname "$(dirname "$SCRIPT_DIR")")")/starbucks"
        log_info "检测到项目根目录(模式3): $PROJECT_ROOT"
    else
        # 尝试直接查找当前工作目录
        if [[ -f "$(pwd)/src/api/main.py" ]]; then
            PROJECT_ROOT="$(pwd)"
            log_info "检测到项目根目录(当前目录): $PROJECT_ROOT"
        elif [[ -f "$(pwd)/../src/api/main.py" ]]; then
            PROJECT_ROOT="$(dirname "$(pwd)")"
            log_info "检测到项目根目录(上级目录): $PROJECT_ROOT"
        fi
    fi

    # 调试信息
    log_info "查找关键文件:"
    log_info "  $SCRIPT_DIR/../src/api/main.py: $(test -f "$SCRIPT_DIR/../src/api/main.py" && echo "存在" || echo "不存在")"
    log_info "  $SCRIPT_DIR/../requirements.txt: $(test -f "$SCRIPT_DIR/../requirements.txt" && echo "存在" || echo "不存在")"

    # 复制项目文件
    if [[ -n "$PROJECT_ROOT" && -d "$PROJECT_ROOT" ]]; then
        log_info "从 $PROJECT_ROOT 复制项目文件"

        # 复制所有文件，包括隐藏文件
        cp -r "$PROJECT_ROOT"/* "$USER_PROJECT_DIR/" 2>/dev/null || true

        # 单独复制隐藏文件（如.env）
        cp "$PROJECT_ROOT"/.[^.]* "$USER_PROJECT_DIR/" 2>/dev/null || true

        # 验证关键文件是否复制成功
        if [[ -f "$USER_PROJECT_DIR/src/api/main.py" ]]; then
            log_info "项目文件复制成功，检测到核心文件"
        else
            log_warn "项目文件复制可能不完整"
        fi

        # 验证.env文件是否复制成功
        if [[ -f "$USER_PROJECT_DIR/.env" ]]; then
            log_info "环境配置文件复制成功: .env"
        else
            log_warn "未找到.env文件，将创建默认配置"
        fi
    else
        # 如果找不到项目文件，创建基本结构
        log_warn "未找到starbucks项目文件，创建基本目录结构"
        mkdir -p "$USER_PROJECT_DIR/src/api"
        mkdir -p "$USER_PROJECT_DIR/src/core"
        mkdir -p "$USER_PROJECT_DIR/src/utils"
        mkdir -p "$USER_PROJECT_DIR/scripts"
        touch "$USER_PROJECT_DIR/requirements.txt"
        touch "$USER_PROJECT_DIR/.env"
        echo "# 星巴克F5绕过系统主程序" > "$USER_PROJECT_DIR/src/api/main.py"
    fi

    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$USER_PROJECT_DIR"
    if [[ -d "$USER_PROJECT_DIR/scripts" ]]; then
        chmod +x "$USER_PROJECT_DIR/scripts/"*.sh 2>/dev/null || true
    fi

    log_info "项目文件复制完成: $USER_PROJECT_DIR"
}

# 创建部署日志
create_deploy_log() {
    log_step "创建部署日志"
    
    LOG_FILE="$DEPLOY_HOME/logs/deploy.log"
    cat > "$LOG_FILE" << EOF
========================================
星巴克F5 Shape绕过系统部署日志
========================================
创建时间: $(date '+%Y-%m-%d %H:%M:%S')
部署用户: $DEPLOY_USER
项目目录: /opt/starbucks-bypass
用户目录: $DEPLOY_HOME

部署步骤:
1. 切换到部署用户: su - $DEPLOY_USER
2. 进入用户项目目录: cd ~/starbucks
3. 执行部署脚本: sudo ./scripts/deploy.sh
4. 验证部署: ./scripts/test_api.sh
5. 部署完成后删除用户: exit && sudo ./starbucks/scripts/delete_deploy_user.sh

重要提醒:
- 项目文件已复制到用户目录: $DEPLOY_HOME/starbucks
- 删除用户将完全清除所有项目相关文件
- 即用即删，不留任何痕迹

注意事项:
- 部署脚本禁止root用户执行
- 部署完成后必须删除此用户
- 所有操作都会记录在此日志中

========================================
EOF
    
    chown "$DEPLOY_USER:$DEPLOY_USER" "$LOG_FILE"
    
    log_info "部署日志创建完成: $LOG_FILE"
}

# 显示用户信息
show_user_info() {
    log_step "部署用户创建完成"
    
    echo ""
    echo "=========================================="
    echo "部署用户信息"
    echo "=========================================="
    echo ""
    echo "用户名: $DEPLOY_USER"
    echo "密码: $DEPLOY_PASSWORD"
    echo "用户目录: $DEPLOY_HOME"
    echo "项目目录: $DEPLOY_HOME/starbucks"
    echo ""
    echo "切换到部署用户:"
    echo "  su - $DEPLOY_USER"
    echo ""
    echo "执行部署:"
    echo "  cd ~/starbucks"
    echo "  sudo ./scripts/deploy.sh"
    echo ""
    echo "部署完成后删除用户:"
    echo "  exit"
    echo "  sudo ./starbucks/scripts/delete_deploy_user.sh"
    echo ""
    echo "注意: 删除用户将完全清除所有项目文件，不留痕迹"
    echo ""
    echo "=========================================="
    echo ""
}

# 完全删除用户（内部函数）
delete_user_completely() {
    log_step "删除现有用户 $DEPLOY_USER"
    
    # 停止用户进程
    pkill -u "$DEPLOY_USER" 2>/dev/null || true
    
    # 删除用户和家目录
    userdel -r "$DEPLOY_USER" 2>/dev/null || true
    
    # 删除sudo配置
    rm -f "/etc/sudoers.d/$DEPLOY_USER"
    
    # 删除项目目录
    rm -rf "/opt/starbucks-bypass"
    
    log_info "用户 $DEPLOY_USER 已完全删除"
}

# 主函数
main() {
    log_info "开始创建部署用户"
    
    check_root
    check_user_exists
    create_deploy_user
    setup_user_environment
    copy_project_files
    create_deploy_log
    show_user_info
    
    log_info "部署用户创建完成！"
    log_warn "请记住：部署完成后必须删除此用户"
}

# 执行主函数
main "$@"
