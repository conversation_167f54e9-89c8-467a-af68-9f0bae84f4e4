#!/bin/bash

# 测试监控后台功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

echo "=========================================="
echo "测试监控后台功能"
echo "=========================================="

# 配置变量
MAIN_SYSTEM_URL="http://localhost:8000"
MONITOR_URL="http://localhost:9094"
CUSTOMER_API_KEY="demo_api_key_change_in_production"
MONITOR_TOKEN="monitor_backend_secret_token_2025"

log_step "1. 测试监控后台健康检查"
if curl -s "$MONITOR_URL/health" > /dev/null; then
    log_info "监控后台健康检查通过"
    HEALTH_RESPONSE=$(curl -s "$MONITOR_URL/health")
    echo "响应: $HEALTH_RESPONSE"
else
    log_error "监控后台健康检查失败"
    exit 1
fi

echo ""
log_step "2. 测试主系统健康检查"
if curl -s "$MAIN_SYSTEM_URL/health" > /dev/null; then
    log_info "主系统健康检查通过"
    HEALTH_RESPONSE=$(curl -s "$MAIN_SYSTEM_URL/health")
    echo "响应: $HEALTH_RESPONSE"
else
    log_error "主系统健康检查失败，请先启动主系统"
    echo "启动命令: cd ~/starbucks && python -m src.api.main"
    exit 1
fi

echo ""
log_step "3. 测试客户API调用（这会触发监控记录）"
echo "使用API密钥: $CUSTOMER_API_KEY"
echo "调用客户测试服务..."

# 调用客户API
CUSTOMER_RESPONSE=$(curl -s -X POST "$MAIN_SYSTEM_URL/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $CUSTOMER_API_KEY" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "test_config": {
      "device_count": 3,
      "method": "GET",
      "concurrent_limit": 2,
      "delay_between_requests": 0.5
    }
  }' || echo "API调用失败")

if [[ "$CUSTOMER_RESPONSE" == *"success"* ]]; then
    log_info "客户API调用成功"
    echo "响应: $CUSTOMER_RESPONSE"
else
    log_warn "客户API调用可能失败"
    echo "响应: $CUSTOMER_RESPONSE"
fi

echo ""
log_step "4. 等待监控记录处理"
log_info "等待5秒让监控后台处理日志..."
sleep 5

echo ""
log_step "5. 检查监控后台是否收到日志"
echo "查询监控日志..."

LOGS_RESPONSE=$(curl -s -X GET "$MONITOR_URL/api/logs?limit=10" \
  -H "Authorization: Bearer $MONITOR_TOKEN" || echo "获取日志失败")

if [[ "$LOGS_RESPONSE" == *"success"* ]]; then
    log_info "成功获取监控日志"
    echo "日志响应: $LOGS_RESPONSE"
    
    # 检查是否有客户日志
    if [[ "$LOGS_RESPONSE" == *"demo_customer"* ]] || [[ "$LOGS_RESPONSE" == *"customer"* ]]; then
        log_info "监控功能正常！发现客户日志记录"
    else
        log_warn "未发现客户日志记录，可能需要检查配置"
    fi
else
    log_error "获取监控日志失败"
    echo "响应: $LOGS_RESPONSE"
fi

echo ""
log_step "6. 测试监控后台统计API"
STATS_RESPONSE=$(curl -s -X GET "$MONITOR_URL/api/stats/customers" \
  -H "Authorization: Bearer $MONITOR_TOKEN" || echo "获取统计失败")

if [[ "$STATS_RESPONSE" == *"success"* ]]; then
    log_info "成功获取客户统计"
    echo "统计响应: $STATS_RESPONSE"
else
    log_warn "获取客户统计失败"
    echo "响应: $STATS_RESPONSE"
fi

echo ""
log_step "7. 测试多次API调用（生成更多监控数据）"
log_info "发送3次测试请求..."

for i in {1..3}; do
    echo "发送第 $i 次请求..."
    curl -s -X POST "$MAIN_SYSTEM_URL/api/bypass/test-service" \
      -H "Content-Type: application/json" \
      -H "X-API-Key: $CUSTOMER_API_KEY" \
      -d "{
        \"target_url\": \"https://httpbin.org/delay/1\",
        \"test_config\": {
          \"device_count\": 2,
          \"method\": \"GET\"
        }
      }" > /dev/null
    sleep 2
done

log_info "等待5秒处理..."
sleep 5

echo ""
log_step "8. 再次检查监控记录"
FINAL_LOGS=$(curl -s -X GET "$MONITOR_URL/api/logs?limit=20" \
  -H "Authorization: Bearer $MONITOR_TOKEN" || echo "获取日志失败")

if [[ "$FINAL_LOGS" == *"success"* ]]; then
    # 计算日志数量
    LOG_COUNT=$(echo "$FINAL_LOGS" | grep -o '"timestamp"' | wc -l)
    log_info "监控后台共记录了 $LOG_COUNT 条日志"
    
    if [ "$LOG_COUNT" -gt 0 ]; then
        log_info "监控功能完全正常！"
        echo ""
        echo "监控验证结果:"
        echo "监控后台运行正常"
        echo "主系统API正常"
        echo "客户API调用成功"
        echo "监控日志记录正常"
        echo "统计功能正常"
        echo ""
        echo "现在你可以:"
        echo "1. 访问监控后台: http://服务器IP:9094/login"
        echo "2. 登录账户: admin / admin123456"
        echo "3. 查看实时监控数据和客户API调用记录"
    else
        log_warn "监控后台运行正常，但未记录到客户日志"
    fi
else
    log_error "最终日志检查失败"
fi

echo ""
echo "=========================================="
echo "监控测试完成"
echo "=========================================="
