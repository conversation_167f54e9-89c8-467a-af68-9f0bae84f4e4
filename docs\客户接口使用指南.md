# 星巴克风控绕过系统 - 客户接口使用指南

## 快速开始

### 接口基本信息
- **服务地址**: `http://您的服务器IP:8094`
- **接口路径**: `/api/bypass/test-service`
- **请求方式**: `POST`
- **认证方式**: API密钥认证

### 获取API密钥

请联系技术支持获取您的专属API密钥。

---

## 接口详细说明

### 1. 请求格式

#### 请求头设置
```
Content-Type: application/json
X-API-Key: 访问API密钥
```

#### 请求体参数
```json
{
  "target_url": "目标网站URL",
  "test_config": {
    "device_count": 设备数量,
    "method": "请求方法",
    "concurrent_limit": 并发限制,
    "delay_between_requests": 请求间隔
  }
}
```

#### 参数说明
- **target_url**: 要测试的目标网站URL（必填）
- **device_count**: 模拟设备数量，范围1-30（可选，默认5）
- **method**: HTTP请求方法，支持GET/POST（可选，默认GET）
- **concurrent_limit**: 并发请求限制（可选，默认10）
- **delay_between_requests**: 请求间隔秒数（可选，默认1.0）

### 2. 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "风控绕过测试完成",
  "data": {
    "test_id": "测试任务ID",
    "target_url": "目标URL",
    "device_count": 使用设备数量,
    "success_rate": 成功率,
    "total_requests": 总请求数,
    "successful_requests": 成功请求数,
    "failed_requests": 失败请求数,
    "average_response_time": 平均响应时间,
    "bypass_effectiveness": "绕过效果评估",
    "risk_level": "风险等级",
    "recommendations": ["建议列表"]
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "错误代码"
}
```

---

## 使用示例

### 示例1: 基础测试
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{
    "target_url": "https://www.starbucks.com.cn/api/check"
  }'
```

### 示例2: 高级配置测试
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{
    "target_url": "https://www.starbucks.com.cn/api/check",
    "test_config": {
      "device_count": 10,
      "method": "POST",
      "concurrent_limit": 5,
      "delay_between_requests": 0.5
    }
  }'
```

### 示例3: Python调用
```python
import requests
import json

# 配置信息
api_url = "http://您的服务器IP:8094/api/bypass/test-service"
api_key = "您的API密钥"

# 请求头
headers = {
    "Content-Type": "application/json",
    "X-API-Key": api_key
}

# 请求数据
data = {
    "target_url": "https://www.starbucks.com.cn/api/check",
    "test_config": {
        "device_count": 8,
        "method": "GET",
        "concurrent_limit": 4,
        "delay_between_requests": 1.0
    }
}

# 发送请求
response = requests.post(api_url, headers=headers, json=data)

# 处理响应
if response.status_code == 200:
    result = response.json()
    if result["success"]:
        print("测试成功！")
        print(f"成功率: {result['data']['success_rate']}%")
        print(f"绕过效果: {result['data']['bypass_effectiveness']}")
    else:
        print(f"测试失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

### 示例4: JavaScript调用
```javascript
// 配置信息
const apiUrl = "http://您的服务器IP:8094/api/bypass/test-service";
const apiKey = "您的API密钥";

// 请求数据
const requestData = {
    target_url: "https://www.starbucks.com.cn/api/check",
    test_config: {
        device_count: 6,
        method: "GET",
        concurrent_limit: 3,
        delay_between_requests: 1.5
    }
};

// 发送请求
fetch(apiUrl, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
    },
    body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('测试成功！');
        console.log('成功率:', data.data.success_rate + '%');
        console.log('绕过效果:', data.data.bypass_effectiveness);
    } else {
        console.log('测试失败:', data.message);
    }
})
.catch(error => {
    console.error('请求错误:', error);
});
```

---

## 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| AUTH_001 | API密钥无效 | 检查API密钥是否正确 |
| AUTH_002 | API密钥已过期 | 联系技术支持更新密钥 |
| PARAM_001 | 参数格式错误 | 检查请求参数格式 |
| PARAM_002 | 必填参数缺失 | 补充必填参数 |
| LIMIT_001 | 请求频率超限 | 降低请求频率 |
| LIMIT_002 | 设备数量超限 | 减少设备数量（最大30） |
| TARGET_001 | 目标URL无效 | 检查URL格式 |
| TARGET_002 | 目标服务器无响应 | 检查目标服务器状态 |
| SYSTEM_001 | 系统繁忙 | 稍后重试 |
| SYSTEM_002 | 服务维护中 | 等待维护完成 |

---

## 最佳实践建议

### 1. 请求频率控制
- 建议每次请求间隔至少1秒
- 避免短时间内大量并发请求
- 根据目标服务器性能调整并发数

### 2. 参数优化建议
- **设备数量**: 一般建议5-15台，过多可能被检测
- **请求间隔**: 建议0.5-2秒，模拟真实用户行为
- **并发限制**: 建议不超过设备数量的50%

### 3. 结果解读
- **成功率 > 80%**: 绕过效果良好
- **成功率 60-80%**: 绕过效果一般，建议调整参数
- **成功率 < 60%**: 绕过效果较差，需要优化策略

### 4. 安全注意事项
- 妥善保管API密钥，避免泄露
- 仅在授权范围内使用接口
- 遵守目标网站的使用条款

---

## 技术支持

### 联系方式
- **技术支持**: 请通过指定渠道联系
- **服务时间**: 7x24小时
- **响应时间**: 工作时间内1小时内响应

### 常见问题
1. **Q: API密钥如何获取？**
   A: 请联系技术支持，提供您的业务需求说明。

2. **Q: 可以测试哪些网站？**
   A: 支持大部分使用F5 Shape防护的网站，具体可咨询技术支持。

3. **Q: 有请求次数限制吗？**
   A: 根据您的套餐有不同的限制，详情请咨询技术支持。

4. **Q: 如何提高绕过成功率？**
   A: 建议根据目标网站特点调整设备数量和请求间隔，或联系技术支持获取优化建议。

---

## 在线测试工具

### Postman测试步骤
1. 打开Postman软件
2. 创建新的POST请求
3. 输入URL: `http://您的服务器IP:8094/api/bypass/test-service`
4. 在Headers中添加:
   - `Content-Type`: `application/json`
   - `X-API-Key`: `您的API密钥`
5. 在Body中选择raw和JSON格式，输入:
```json
{
  "target_url": "https://www.starbucks.com.cn/api/check"
}
```
6. 点击Send发送请求

### 浏览器测试（开发者工具）
```javascript
// 在浏览器控制台中执行
fetch('http://您的服务器IP:8094/api/bypass/test-service', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': '您的API密钥'
  },
  body: JSON.stringify({
    target_url: 'https://www.starbucks.com.cn/api/check'
  })
}).then(r => r.json()).then(console.log);
```

---

## 更新日志

- **2025-08-01**: 初始版本发布
- 支持基础风控绕过测试
- 提供多种编程语言示例
- 完善错误处理和状态码
- 添加在线测试工具说明
