#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星巴克风控绕过系统 - 监控后台测试脚本
测试监控后台的基本功能和Web界面
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path

def test_monitor_backend():
    """测试监控后台功能"""
    print("=" * 60)
    print("星巴克风控绕过系统 - 监控后台测试")
    print("=" * 60)
    
    # 检查文件结构
    print("\n1. 检查文件结构...")
    required_files = [
        "src/monitor_app.py",
        "src/templates/login.html",
        "src/templates/dashboard.html",
        "src/static/css/login.css",
        "src/static/css/dashboard.css",
        "src/static/js/login.js",
        "src/static/js/dashboard.js",
        "requirements.txt",
        "deploy_monitor.sh"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  [完成] {file_path}")

    if missing_files:
        print(f"\n[失败] 缺少文件: {missing_files}")
        return False
    
    print("  [完成] 所有必需文件都存在")
    
    # 检查Python依赖
    print("\n2. 检查Python依赖...")
    try:
        import fastapi
        import uvicorn
        import jinja2
        import jwt
        print("  [完成] 核心依赖包已安装")
    except ImportError as e:
        print(f"  [失败] 缺少依赖包: {e}")
        print("  请运行: pip install -r requirements.txt")
        return False
    
    # 测试应用导入
    print("\n3. 测试应用导入...")
    try:
        sys.path.insert(0, os.path.join(os.getcwd(), 'src'))
        from monitor_app import app
        print("  [完成] 监控应用导入成功")
    except Exception as e:
        print(f"  [失败] 应用导入失败: {e}")
        return False
    
    # 测试基本配置
    print("\n4. 测试基本配置...")
    try:
        # 检查环境变量
        os.environ.setdefault('ADMIN_USERNAME', 'admin')
        os.environ.setdefault('ADMIN_PASSWORD', 'admin123456')
        os.environ.setdefault('JWT_SECRET_KEY', 'test_secret_key')
        print("  [完成] 环境变量配置完成")
    except Exception as e:
        print(f"  [失败] 配置失败: {e}")
        return False
    
    print("\n5. 启动测试服务器...")
    print("  监控后台将在 http://localhost:9000 启动")
    print("  登录账户: admin")
    print("  登录密码: admin123456")
    print("\n  按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    # 启动服务器
    try:
        import uvicorn
        uvicorn.run(
            "monitor_app:app",
            host="0.0.0.0",
            port=9000,
            reload=True,
            app_dir="src"
        )
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
        return True
    except Exception as e:
        print(f"\n[失败] 服务器启动失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n6. 测试API端点...")
    base_url = "http://localhost:9000"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("  [完成] 健康检查端点正常")
        else:
            print(f"  [失败] 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"  [失败] 无法连接到服务器: {e}")
        return False
    
    # 测试登录页面
    try:
        response = requests.get(f"{base_url}/login", timeout=5)
        if response.status_code == 200:
            print("  [完成] 登录页面正常")
        else:
            print(f"  [失败] 登录页面失败: {response.status_code}")
    except Exception as e:
        print(f"  [失败] 登录页面请求失败: {e}")
    
    # 测试登录API
    try:
        login_data = {
            "username": "admin",
            "password": "admin123456",
            "remember_me": False
        }
        response = requests.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("  [完成] 登录API正常")
                return result.get("access_token")
            else:
                print(f"  [失败] 登录失败: {result.get('message')}")
        else:
            print(f"  [失败] 登录API失败: {response.status_code}")
    except Exception as e:
        print(f"  [失败] 登录API请求失败: {e}")
    
    return None

def show_usage():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("监控后台测试完成")
    print("=" * 60)
    print("\n使用说明:")
    print("1. 在浏览器中访问: http://localhost:9000")
    print("2. 使用以下凭据登录:")
    print("   - 用户名: admin")
    print("   - 密码: admin123456")
    print("3. 登录后可以查看监控后台界面")
    print("\n部署说明:")
    print("1. 生产环境部署: sudo bash deploy_monitor.sh")
    print("2. 服务管理: sudo systemctl start/stop/restart monitor-backend")
    print("3. 查看日志: sudo journalctl -u monitor-backend -f")
    print("\n注意事项:")
    print("- 监控后台独立于主系统运行")
    print("- 生产环境请修改默认密码")
    print("- 确保防火墙开放9000端口")

if __name__ == "__main__":
    print("星巴克风控绕过系统 - 监控后台测试工具")
    
    # 检查是否在正确的目录
    if not os.path.exists("src/monitor_app.py"):
        print("[失败] 请在monitor_backend目录下运行此脚本")
        sys.exit(1)
    
    try:
        # 运行测试
        success = test_monitor_backend()
        
        if success:
            show_usage()
            print("\n[完成] 监控后台测试完成")
        else:
            print("\n[失败] 监控后台测试失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n[失败] 测试过程中发生错误: {e}")
        sys.exit(1)
