设备指纹接口测试 - F5 Shape指纹生成
===========================================

重要更新: 系统已优化，现在每次调用都会生成全新的指纹，确保100%唯一性！
🔥 并发唯一性保证: 30台设备同时并发时，指纹绝对不会重复！

认证信息:
API密钥: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
服务器: http://38.150.2.100:8094

===========================================
1. 批量生成设备指纹（推荐使用）
===========================================

接口: POST /api/v1/fingerprint/generate

测试命令:
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 5,
    "force_regenerate": false
  }'

参数说明:
- device_count: 生成指纹数量（1-30）
- force_regenerate: 是否强制重新生成（默认true，确保每次指纹都不同）

响应示例:
{
  "success": true,
  "message": "成功生成 5 个设备指纹",
  "fingerprints": [
    {
      "x-device-id": "device_0_1722518400",
      "X-XHPAcPXq-z": "static_value",
      "X-XHPAcPXq-g": "base64_encoded_data...",
      "X-XHPAcPXq-e": "base64_encoded_data...",
      "X-XHPAcPXq-f": "base64_encoded_data...",
      "X-XHPAcPXq-d": "encoded_data...",
      "X-XHPAcPXq-c": "base64_encoded_data...",
      "X-XHPAcPXq-b": "encoded_data...",
      "X-XHPAcPXq-a": "base64_encoded_data...",
      "time": "2025-8-1 22:30:00"
    }
  ]
}

===========================================
2. 获取单个设备指纹
===========================================

接口: GET /api/v1/fingerprint/{device_index}

测试命令:
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

说明: 获取设备0的指纹（设备索引范围: 0-29）

===========================================
3. 查看设备状态
===========================================

接口: GET /api/v1/devices

测试命令:
curl -X GET "http://38.150.2.100:8094/api/v1/devices" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

响应示例:
{
  "success": true,
  "message": "获取设备列表成功",
  "data": {
    "total_devices": 30,
    "available_devices": 28,
    "banned_devices": 2,
    "devices": [
      {
        "index": 0,
        "status": "available",
        "last_used": "2025-8-1 22:25:00",
        "success_count": 15,
        "fail_count": 1
      }
    ]
  }
}

===========================================
4. 指纹使用方法
===========================================

使用获取的指纹访问目标网站:
curl -X GET "https://目标网站.com/api/endpoint" \
  -H "x-device-id: device_0_1722518400" \
  -H "X-XHPAcPXq-z: static_value" \
  -H "X-XHPAcPXq-g: base64_encoded_data..." \
  -H "X-XHPAcPXq-e: base64_encoded_data..." \
  -H "X-XHPAcPXq-f: base64_encoded_data..." \
  -H "X-XHPAcPXq-d: encoded_data..." \
  -H "X-XHPAcPXq-c: base64_encoded_data..." \
  -H "X-XHPAcPXq-b: encoded_data..." \
  -H "X-XHPAcPXq-a: base64_encoded_data..." \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

===========================================
5. 测试场景
===========================================

场景1: 基础指纹获取测试
# 获取5个指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 5}'

# 查看设备状态
curl -X GET "http://38.150.2.100:8094/api/v1/devices" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 获取单个指纹
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

场景2: 大批量指纹生成（30台设备并发唯一性保证）
# 生成30个指纹（最大数量）- 每个指纹都完全不同
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 30, "force_regenerate": true}'

# 重要说明: 30台设备并发时，系统保证每个指纹都完全唯一，绝不重复！

场景3: 强制重新生成指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 10, "force_regenerate": true}'

===========================================
6. F5 Shape指纹字段说明
===========================================

x-device-id: 设备唯一标识符
X-XHPAcPXq-z: 静态值标识
X-XHPAcPXq-g: 设备特征编码
X-XHPAcPXq-e: 环境信息编码
X-XHPAcPXq-f: 功能特征编码
X-XHPAcPXq-d: 动态数据编码
X-XHPAcPXq-c: 配置信息编码
X-XHPAcPXq-b: 行为特征编码
X-XHPAcPXq-a: 算法验证编码
time: 生成时间戳

指纹特点:
- 唯一性: 每个设备指纹都是唯一的
- 有效性: 指纹可以有效绕过F5 Shape检测
- 稳定性: 指纹在一定时间内保持稳定
- 真实性: 基于真实设备特征生成

===========================================
7. 集成步骤
===========================================

步骤1: 获取指纹
调用生成接口获取指纹

步骤2: 解析响应
提取JSON响应中的指纹字段

步骤3: 构建请求
将指纹字段添加到HTTP请求头

步骤4: 发送请求
访问目标网站

步骤5: 监控结果
检查请求是否成功绕过风控

===========================================
8. 30台设备并发指纹唯一性测试
===========================================

接口: POST /api/v1/test/concurrent

重要保证: 30台设备同时并发时，指纹绝对不会重复！

测试命令:
curl -X POST "http://38.150.2.100:8094/api/v1/test/concurrent" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "concurrent_count": 30,
    "test_endpoint": "https://www.starbucks.com.cn/api/customer/info"
  }'

参数说明:
- concurrent_count: 并发设备数量（1-30）
- test_endpoint: 测试目标端点

响应示例:
{
  "success": true,
  "message": "并发测试完成",
  "data": {
    "test_results": [
      {
        "device_index": 123456,
        "device_id": "A1B2C3D4-E5F6-7890-ABCD-EF1234567890",
        "device_fingerprint": {
          "x-device-id": "A1B2C3D4-E5F6-7890-ABCD-EF1234567890",
          "X-XHPAcPXq-g": "unique_base64_data_1...",
          "X-XHPAcPXq-e": "unique_base64_data_2...",
          "time": "2025-8-1 22:30:00"
        },
        "success": true,
        "response_time": 0.156
      }
    ],
    "summary": {
      "total_devices": 30,
      "successful_tests": 30,
      "success_rate": 1.0,
      "all_fingerprints_unique": true
    }
  }
}

唯一性验证:
- 系统自动验证30个指纹的完全唯一性
- all_fingerprints_unique: true 表示所有指纹都不重复
- 如果发现重复，系统会自动报错并重新生成

===========================================
9. 错误处理
===========================================

400错误: 参数错误，检查device_count范围（1-30）
401错误: API密钥无效，检查密钥是否正确
404错误: 设备不存在，检查设备索引范围
500错误: 服务器内部错误，联系技术支持

===========================================
快速测试命令汇总
===========================================

# 1. 生成5个指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" -H "Content-Type: application/json" -d '{"device_count": 5}'

# 2. 查看设备状态
curl -X GET "http://38.150.2.100:8094/api/v1/devices" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 3. 获取单个指纹
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 4. 生成最大数量指纹（30个完全唯一）
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" -H "Content-Type: application/json" -d '{"device_count": 30}'

# 5. 30台设备并发唯一性测试
curl -X POST "http://38.150.2.100:8094/api/v1/test/concurrent" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" -H "Content-Type: application/json" -d '{"concurrent_count": 30, "test_endpoint": "https://www.starbucks.com.cn/api/customer/info"}'

# 5. 真实验证测试（新增）
curl -X POST "http://38.150.2.100:8094/api/v1/verify/real-test" -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" -H "Content-Type: application/json" -d '{"target_url": "https://your-test-endpoint.com/api/test", "test_method": "GET", "expected_block_status": 469}'

===========================================
9. 真实验证接口（重要更新）
===========================================

针对客户关于"469状态码"和"真实绕过效果"的担心，系统新增真实验证接口。

接口地址: POST /api/v1/verify/real-test

功能说明:
- 客户可以用自己的测试端点验证系统真实绕过能力
- 系统会先发送无指纹请求作为基准
- 再发送带指纹请求进行对比
- 特别针对469等风控状态码进行检测

请求参数:
{
  "target_url": "客户的测试端点",
  "test_method": "GET",
  "expected_block_status": 469
}

验证流程:
1. 基准测试: 无指纹请求（应该被风控拦截）
2. 指纹测试: 使用生成指纹请求
3. 对比分析: 计算绕过效果评分
4. 结果报告: 提供详细的验证证据

响应字段说明:
- real_bypass_confirmed: 是否确认真实绕过
- credibility_score: 可信度评分（0-1）
- baseline_test: 基准测试结果
- fingerprint_test: 指纹测试结果
- comparison_analysis: 对比分析结果

使用建议:
1. 选择确定会返回469状态码的测试端点
2. 先进行基准测试确认会被拦截
3. 使用验证接口对比绕过效果
4. 根据credibility_score评估真实性

===========================================
10. 系统增强说明
===========================================

针对客户反馈，系统已进行以下增强:

1. 风控检测增强:
   - 新增469状态码检测
   - 增加更多风控状态码: 418, 444, 460, 499, 521, 523, 525
   - 增强响应内容分析
   - 特别检测JSON中的错误码

2. 指纹唯一性保证:
   - 默认force_regenerate=true
   - 动态设备索引生成
   - 时间戳+随机数确保唯一性

3. 真实性验证:
   - 新增真实验证接口
   - 基准对比测试
   - 详细的绕过效果分析
   - 透明的验证过程

4. 日志增强:
   - 详细的风控检测日志
   - 状态码分析记录
   - 绕过效果评分记录

===========================================
注意事项
===========================================

1. 所有请求都需要在Header中包含API密钥：X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
2. 系统默认force_regenerate=true，确保每次生成不同的指纹
3. 建议在正式使用前先进行小规模测试
4. 系统支持最大30个并发设备，并保证指纹绝对不重复
5. 请合理控制请求频率，避免对目标服务器造成过大压力
6. 【新增】使用真实验证接口可以确认系统的实际绕过效果
7. 【新增】系统已增强469状态码检测，提高风控识别准确性
8. 【新增】如对绕过效果有疑问，请使用真实验证接口进行验证
9. 【重要】30台设备并发时指纹100%唯一，绝不重复，可使用并发测试接口验证

技术支持: 如有问题请联系技术支持团队
