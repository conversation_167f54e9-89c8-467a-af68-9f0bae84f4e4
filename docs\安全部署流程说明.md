# 星巴克F5 Shape绕过系统安全部署流程

**作者**: YINGAshadow  
**日期**: 2025-07-29  
**版本**: v1.0  

## 概述

本文档描述了星巴克F5 Shape绕过系统的安全部署流程，采用专用用户部署机制，确保系统安全性，避免root用户直接操作带来的风险。

## 安全设计原则

### 1. 用户隔离
- **禁止root直接部署**: 防止系统意外损坏
- **专用部署用户**: 使用固定的部署用户`sbdeploy`
- **即用即删**: 部署完成后立即删除部署用户
- **权限最小化**: 仅授予必要的sudo权限

### 2. 不留痕迹
- **完全清理**: 删除用户账户、家目录、配置文件
- **日志清理**: 清除系统日志中的用户记录
- **历史清理**: 清除命令历史和网络连接
- **备份保护**: 重要文件自动备份

## 部署用户配置

### 固定用户信息
- **用户名**: `sbdeploy`
- **密码**: `SB2025Deploy#888`
- **用户组**: `sudo`
- **家目录**: `/home/<USER>
- **项目目录**: `/opt/starbucks-bypass`

### sudo权限配置
部署用户仅授予以下命令的sudo权限：
```bash
# 系统服务管理
/bin/systemctl, /usr/bin/systemctl

# 防火墙管理
/usr/sbin/ufw, /bin/ufw

# 包管理
/usr/bin/apt, /bin/apt

# Web服务器
/usr/bin/nginx, /bin/nginx

# 文件操作
/bin/mkdir, /bin/chown, /bin/chmod, /bin/ln, /bin/rm
```

## 完整部署流程

### 第一步：创建部署用户
```bash
# 以root身份执行
sudo ./scripts/create_deploy_user.sh
```

**执行内容**：
- 创建专用部署用户`sbdeploy`
- 配置sudo权限（仅限必要命令）
- 设置用户环境和别名
- 复制项目文件到`/opt/starbucks-bypass`
- 创建部署日志文件

**输出信息**：
```
========================================
部署用户信息
========================================

用户名: sbdeploy
密码: SB2025Deploy#888
用户目录: /home/<USER>
项目目录: /opt/starbucks-bypass

切换到部署用户:
  su - sbdeploy

执行部署:
  cd /opt/starbucks-bypass
  sudo ./scripts/deploy.sh

部署完成后删除用户:
  exit
  sudo ./scripts/delete_deploy_user.sh
```

### 第二步：切换到部署用户
```bash
# 切换到部署用户
su - sbdeploy
```

**用户环境**：
- 自动显示部署信息和可用命令
- 设置项目相关环境变量
- 提供便捷的管理别名

### 第三步：执行系统部署
```bash
# 进入项目目录
cd /opt/starbucks-bypass

# 执行部署脚本（禁止root执行）
sudo ./scripts/deploy.sh
```

**安全检查**：
- 自动检测并拒绝root用户执行
- 验证当前用户的sudo权限
- 推荐使用专用部署用户

**部署内容**：
- 安装系统依赖包
- 创建Python虚拟环境
- 安装Python依赖
- 配置systemd服务
- 配置Nginx反向代理
- 配置防火墙规则
- 启动所有服务

### 第四步：验证部署结果
```bash
# 验证部署
./scripts/verify_deployment.sh

# 测试API接口
./scripts/test_api.sh

# 检查服务状态
status  # 别名命令

# 查看日志
logs   # 别名命令
```

### 第五步：退出部署用户
```bash
# 退出部署用户
exit
```

### 第六步：删除部署用户
```bash
# 以root身份删除部署用户
sudo ./scripts/delete_deploy_user.sh
```

**删除确认**：
需要输入`DELETE`确认删除操作

**删除内容**：
- 备份重要文件到临时目录
- 停止所有用户进程
- 删除用户账户和家目录
- 删除sudo配置文件
- 清理系统日志记录
- 清理命令历史记录
- 删除项目临时文件
- 清理网络连接

**保留内容**：
- 系统服务（starbucks-bypass）
- Nginx配置
- 防火墙规则
- 备份文件

## 脚本文件说明

### 1. create_deploy_user.sh
- **功能**: 创建专用部署用户
- **权限**: 需要root权限
- **安全**: 检查用户是否已存在，支持重新创建

### 2. deploy.sh
- **功能**: 执行系统部署
- **权限**: 禁止root用户，需要sudo权限
- **安全**: 自动检测用户权限，推荐使用专用用户

### 3. delete_deploy_user.sh
- **功能**: 完全删除部署用户
- **权限**: 需要root权限
- **安全**: 需要确认操作，完全清理不留痕迹

### 4. configure_ssh.sh
- **功能**: 配置SSH端口为28262
- **权限**: 需要root权限
- **安全**: 自动备份配置，支持恢复

## 端口配置

### 最终端口架构
```
外部访问:
├── SSH: 28262 (自定义SSH端口)
└── Web: 8094 (Nginx反向代理)

内部服务:
└── API: 8888 (FastAPI应用，仅本地访问)
```

### 防火墙规则
```bash
# SSH访问
28262/tcp

# Web接口
8094/tcp

# 内部API（仅本地）
127.0.0.1 -> 8888/tcp
```

## 安全注意事项

### 1. 用户管理
- 部署用户密码为固定值，仅用于临时部署
- 部署完成后必须立即删除用户
- 不要在生产环境中长期保留部署用户

### 2. 权限控制
- 部署脚本严格禁止root用户执行
- sudo权限仅限于必要的系统命令
- 所有文件操作都有权限检查

### 3. 日志安全
- 自动清理系统日志中的用户记录
- 清除命令历史和网络连接记录
- 重要操作日志自动备份

### 4. 网络安全
- 自定义SSH端口，避免默认端口扫描
- 防火墙严格控制端口开放
- 内部API仅允许本地访问

## 故障恢复

### 1. 部署失败
```bash
# 查看部署日志
tail -f /home/<USER>/logs/deploy.log

# 查看系统服务状态
sudo systemctl status starbucks-bypass

# 重新部署
cd /opt/starbucks-bypass
sudo ./scripts/deploy.sh
```

### 2. 用户删除失败
```bash
# 手动停止用户进程
sudo pkill -u sbdeploy

# 强制删除用户
sudo userdel -r sbdeploy

# 清理sudo配置
sudo rm -f /etc/sudoers.d/sbdeploy
```

### 3. SSH配置恢复
```bash
# 恢复SSH配置
sudo cp /etc/ssh/sshd_config.backup /etc/ssh/sshd_config
sudo systemctl restart sshd
```

## 总结

本安全部署流程通过以下机制确保系统安全：

1. **用户隔离**: 专用部署用户，避免root直接操作
2. **权限最小化**: 仅授予必要的sudo权限
3. **即用即删**: 部署完成后立即清理用户
4. **不留痕迹**: 完全清理用户相关记录
5. **自动化**: 全程脚本化，减少人为错误
6. **可恢复**: 重要配置自动备份，支持快速恢复

通过严格遵循此流程，可以确保星巴克F5 Shape绕过系统的安全部署，避免系统风险。
