#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件模块
作者：YINGAshadow
创建时间：2025-7-29
功能：系统配置管理
"""

import os
from typing import Optional, List, Dict


class Settings:
    """系统配置类"""
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8888"))
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # 设备配置
    MAX_DEVICES: int = 30
    DEVICE_POOL_SIZE: int = 50
    
    # F5 Shape配置
    F5_SHAPE_VERSION: str = "1.0"
    BASE_DEVICE_ID: str = "B434ED82-107C-483B-B96F-8BE7DFE55B30"
    
    # 固定指纹参数
    FIXED_HEADERS = {
        "X-XHPAcPXq-z": "q",
        "X-XHPAcPXq-f": "A8ElyX6XAQAA3jMXbEgoifVmUL-r1oqNwrOr1VYoC2p_uDTox-YGYxxn8gs9AavVwAADtx9ZAAAO8YLqosIO8Q==",
        "X-XHPAcPXq-d": "ABaQoAOAAKiAhACAAYCQwACIAIAwwAGAAIBAhAChGIAAgICSCADh5xdhhyLFHgAAAAB7F2QeAovkkM5qnL18y6x7wPl2OWQ",
        "X-XHPAcPXq-c": "AOCax36XAQAAwHehWhq_3uSkuO-FD-bDaZe5Md8Yfhq9ZCS-_-HnF2GHIsUe",
        "X-XHPAcPXq-b": "-or34zw"
    }
    
    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./starbucks_devices.db")
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/starbucks_fingerprint.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # API配置
    API_PREFIX: str = "/api/v1"
    API_TITLE: str = "星巴克设备指纹风控绕过系统"
    API_DESCRIPTION: str = "基于F5 Shape技术的设备指纹生成和管理系统"
    API_VERSION: str = "1.0.0"
    
    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "60"))
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = 100
    REQUEST_TIMEOUT: int = 30
    RETRY_COUNT: int = 3
    RETRY_DELAY: int = 1
    
    # 风控测试配置 - 真实星巴克API端点
    STARBUCKS_API_BASE: str = "https://app.starbucks.com.cn"
    TEST_ENDPOINTS: List[str] = [
        "https://app.starbucks.com.cn/bff/ordering/product/list",
        "https://app.starbucks.com.cn/bff/ordering/store/list",
        "https://app.starbucks.com.cn/bff/user/profile",
        "https://app.starbucks.com.cn/bff/promotion/list",
        "https://app.starbucks.com.cn/bff/ordering/cart/add"
    ]
    DEFAULT_TEST_ENDPOINT: str = "https://app.starbucks.com.cn/bff/ordering/product/list"
    SUCCESS_RATE_THRESHOLD: float = 0.8

    # 风控检测标识
    BLOCKED_INDICATORS: List[str] = [
        "风控拦截",
        "请求被拒绝",
        "访问受限",
        "security_check_failed",
        "blocked_by_security",
        "rate_limit_exceeded",
        "suspicious_activity",
        "验证失败",
        "账号异常",
        "设备异常"
    ]
    
    @classmethod
    def get_log_dir(cls) -> str:
        """获取日志目录"""
        log_dir = os.path.dirname(cls.LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        return log_dir
    
    @classmethod
    def get_database_path(cls) -> str:
        """获取数据库路径"""
        db_path = cls.DATABASE_URL.replace("sqlite:///", "")
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        return db_path


# 全局配置实例
settings = Settings()


def load_config_from_env():
    """从环境变量加载配置"""
    settings.HOST = os.getenv("STARBUCKS_HOST", settings.HOST)
    settings.PORT = int(os.getenv("STARBUCKS_PORT", settings.PORT))
    settings.DEBUG = os.getenv("STARBUCKS_DEBUG", "false").lower() == "true"
    settings.MAX_DEVICES = int(os.getenv("STARBUCKS_MAX_DEVICES", settings.MAX_DEVICES))
    settings.LOG_LEVEL = os.getenv("STARBUCKS_LOG_LEVEL", settings.LOG_LEVEL)


def validate_config():
    """验证配置参数"""
    if settings.MAX_DEVICES <= 0:
        raise ValueError("设备数量必须大于0")
    
    if settings.PORT <= 0 or settings.PORT > 65535:
        raise ValueError("端口号必须在1-65535之间")
    
    if settings.DEVICE_POOL_SIZE < settings.MAX_DEVICES:
        raise ValueError("设备池大小必须大于等于最大设备数量")


# 初始化配置
load_config_from_env()
validate_config()
