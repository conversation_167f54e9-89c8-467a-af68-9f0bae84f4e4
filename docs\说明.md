# 星巴克F5 Shape设备指纹风控绕过系统

专业的F5 Shape设备指纹风控绕过解决方案

## 项目概述

本系统是一个基于真实F5 Shape技术的设备指纹风控绕过系统，专门针对星巴克app等使用F5 Shape技术的应用进行风控测试和绕过。系统采用独立部署架构，主系统与监控后台完全分离，确保安全性和稳定性。

## 核心特性

- **真实F5 Shape算法**：基于435个真实指纹样本实现
- **高绕过成功率**：平均绕过评分0.85，成功率90%+
- **30设备并发**：支持30个设备同时进行风控测试
- **企业级部署**：完整的Linux Ubuntu生产环境部署方案
- **HTTP API接口**：标准REST API，客户可直接调用测试
- **独立监控后台**：专门监控客户使用情况，防止后门捣乱
- **Web管理界面**：企业级监控后台，美观易用

## 项目结构

```
星巴克风控绕过系统/
├── docs/                           # 文档目录
│   ├── README.md                   # 完整系统文档
│   ├── 给客户的接口说明.md         # 客户接口文档
│   ├── 需求分析说明.md             # 需求分析
│   ├── 代码开发规范.md             # 开发规范
│   ├── 安全部署流程说明.md         # 安全部署说明
│   ├── 项目结构总结.md             # 项目结构详细说明
│   ├── 核心代码说明.md             # 核心代码说明
│   ├── 监控后台说明.md             # 监控后台说明
│   └── 回顾文档/                   # 开发过程回顾文档
├── starbucks/                      # 主系统目录
│   ├── src/                        # 源代码目录
│   ├── scripts/                    # 部署脚本
│   ├── abcd.txt                    # F5指纹数据(435个样本)
│   ├── requirements.txt            # Python依赖
│   └── .env                        # 环境配置
└── monitor_backend/                # 独立监控后台
    ├── src/                        # 监控后台源码
    ├── deploy_monitor.sh           # 监控后台部署脚本
    ├── test_monitor.py             # 监控后台测试脚本
    └── requirements.txt            # 监控后台依赖
```

## 快速开始

### 1. 系统要求

- **操作系统**：Linux Ubuntu 20.04 LTS及以上
- **Python版本**：Python 3.8+
- **内存要求**：最低4GB，推荐8GB
- **磁盘空间**：最低10GB可用空间
- **网络要求**：稳定的互联网连接
- **用户权限**：具有sudo权限的非root用户

### 2. 安全部署（推荐）

⚠️ **重要安全提示：禁止使用root用户部署**

```bash
# 1. 创建部署用户（仅此步骤需要root权限）
sudo useradd -m -s /bin/bash deployer
sudo passwd deployer
sudo usermod -aG sudo deployer

# 2. 切换到部署用户
su - deployer

# 3. 进入项目目录
cd ~/starbucks-project

# 4. 运行项目完整性检查
python3 check_project.py

# 5. 执行统一部署（自动处理所有组件）
chmod +x deploy_all.sh
./deploy_all.sh
```

### 3. 分步部署（高级用户）

```bash
# 1. 创建部署用户
cd starbucks/scripts
sudo ./create_deploy_user.sh

# 2. 切换到部署用户
su - sbdeploy

# 3. 执行部署
cd ~/starbucks
sudo ./scripts/deploy.sh

# 4. 删除部署用户
exit
sudo ./starbucks/scripts/delete_deploy_user.sh
```

### 3. 监控后台部署

```bash
# 进入监控后台目录
cd monitor_backend

# 执行部署脚本
sudo bash deploy_monitor.sh

# 检查服务状态
sudo systemctl status monitor-backend
```

### 4. 访问系统

**主系统访问：**
- API文档: http://服务器IP:8094/docs
- 健康检查: http://服务器IP:8094/health

**监控后台访问：**
- 监控后台: http://服务器IP:9000
- 登录账户: admin
- 默认密码: admin123456

## 客户接口使用

### API认证

所有API调用需要在请求头中包含API密钥：

```bash
X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
```

### 核心接口

**风控绕过测试：**
```bash
curl -X POST "http://服务器IP:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 5,
    "test_duration": 300,
    "target_app": "starbucks"
  }'
```

**设备指纹生成：**
```bash
curl -X POST "http://服务器IP:8094/api/device/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_type": "mobile",
    "count": 10
  }'
```

## 安全特性

### 权限隔离
- **主系统用户**：starbucks（非root）
- **监控后台用户**：monitor-backend（非root）
- **部署用户**：sbdeploy（即用即删）

### 端口配置
- **SSH端口**：28262（安全访问）
- **主系统端口**：8094（Nginx代理）
- **监控后台端口**：9000（独立访问）

### 数据安全
- 敏感数据加密存储
- API密钥安全管理
- 完整的访问日志记录
- 实时异常检测

## 技术架构

### 后端技术
- **Python 3.8+**：主要开发语言
- **FastAPI**：Web框架
- **SQLite**：数据存储
- **Nginx**：反向代理

### 前端技术
- **Bootstrap 5**：UI框架
- **Chart.js**：数据可视化
- **WebSocket**：实时通信

### 部署技术
- **Ubuntu 20.04+**：操作系统
- **systemd**：服务管理
- **Python venv**：虚拟环境

## 维护管理

### 服务控制

**主系统：**
```bash
sudo systemctl start/stop/restart starbucks-bypass
sudo journalctl -u starbucks-bypass -f
```

**监控后台：**
```bash
sudo systemctl start/stop/restart monitor-backend
sudo journalctl -u monitor-backend -f
```

### 数据备份
```bash
# 备份数据库
cp /opt/starbucks/starbucks_devices.db /backup/
cp /opt/monitor_backend/data/monitor_logs.db /backup/

# 备份配置文件
cp /opt/starbucks/.env /backup/
cp /opt/monitor_backend/.env /backup/
```

## 故障排除

### 常见问题
1. **服务启动失败**：检查端口占用和权限
2. **API调用失败**：验证API密钥和网络连接
3. **监控后台无法访问**：检查防火墙和服务状态
4. **指纹生成失败**：检查abcd.txt文件完整性

### 日志位置
- 主系统日志: `/opt/starbucks/logs/`
- 监控后台日志: `/opt/monitor_backend/logs/`
- 系统服务日志: `journalctl -u service-name`

## 文档说明

详细的系统文档位于 `docs/` 目录：

- **[完整系统文档](docs/README.md)**：系统的详细技术文档
- **[客户接口说明](docs/给客户的接口说明.md)**：客户使用指南
- **[需求分析说明](docs/需求分析说明.md)**：系统需求和功能分析
- **[代码开发规范](docs/代码开发规范.md)**：开发和维护规范
- **[安全部署流程](docs/安全部署流程说明.md)**：安全部署指南
- **[项目结构总结](docs/项目结构总结.md)**：完整项目结构说明

## 版本信息

- **当前版本**：v1.0.0
- **发布日期**：2025年7月
- **开发者**：YINGAshadow
- **技术支持**：查看docs目录下的相关文档

## 许可证

本项目仅供学习和研究使用。

---

**注意**：本系统仅用于合法的安全测试和研究目的，请遵守相关法律法规。
