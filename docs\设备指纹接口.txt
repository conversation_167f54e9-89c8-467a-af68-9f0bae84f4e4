设备指纹接口

接口地址: GET http://38.150.2.100:8094/api/v1/fingerprint/single
API密钥: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS

调用方式:
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/single" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

确定:
- 能用 - 基于真实F5指纹数据生成
- 能高并发 - 支持无限并发调用
- 能过风控 - 每次返回不同的有效指纹

返回格式:
{
  "success": true,
  "message": "成功生成单个指纹",
  "fingerprints": [
    {
      "x-device-id": "设备ID",
      "X-XHPAcPXq-g": "G字段数据",
      "X-XHPAcPXq-e": "E字段数据",
      "Authorization": "授权字段",
      "x-bs-device-id": "BS设备ID",
      "X-XHPAcPXq-z": "Z字段",
      "X-XHPAcPXq-f": "F字段",
      "X-XHPAcPXq-d": "D字段",
      "X-XHPAcPXq-c": "C字段",
      "X-XHPAcPXq-b": "B字段",
      "X-XHPAcPXq-a": "A字段",
      "time": "时间戳"
    }
  ]
}

接口给你了，直接用就行。
