#!/bin/bash

# 删除部署用户脚本
# 作者: YINGAshadow
# 用途: 完全删除部署用户，不留痕迹

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 固定用户配置
DEPLOY_USER="sbdeploy"
DEPLOY_HOME="/home/<USER>"
PROJECT_DIR="/opt/starbucks-bypass"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "删除部署用户只能由root用户执行"
        log_error "当前用户: $(whoami)"
        echo ""
        echo "正确的执行方式："
        echo "  sudo ./starbucks/scripts/delete_deploy_user.sh"
        echo ""
        echo "完整的即用即删流程："
        echo "1. root创建用户: sudo ./starbucks/scripts/create_deploy_user.sh"
        echo "2. 切换用户: su - sbdeploy"
        echo "3. 执行部署: cd ~/starbucks && sudo ./scripts/deploy.sh"
        echo "4. 测试验证: ./scripts/test_api.sh"
        echo "5. 退出用户: exit"
        echo "6. root删除用户: sudo ./starbucks/scripts/delete_deploy_user.sh"
        echo ""
        exit 1
    fi
}

# 检查用户是否存在
check_user_exists() {
    if ! id "$DEPLOY_USER" &>/dev/null; then
        log_warn "用户 $DEPLOY_USER 不存在"
        exit 0
    fi
}

# 确认删除操作
confirm_deletion() {
    echo ""
    echo "=========================================="
    echo "警告：即将删除部署用户"
    echo "=========================================="
    echo ""
    echo "用户名: $DEPLOY_USER"
    echo "用户目录: $DEPLOY_HOME"
    echo "项目目录: $PROJECT_DIR"
    echo ""
    echo "此操作将："
    echo "1. 停止所有用户进程"
    echo "2. 删除用户账户和家目录"
    echo "3. 删除sudo配置文件"
    echo "4. 清理系统日志中的用户记录"
    echo "5. 删除项目临时文件"
    echo "6. 清理命令历史记录"
    echo ""
    
    read -p "确认删除用户 $DEPLOY_USER? (输入 'DELETE' 确认): " -r
    if [[ $REPLY != "DELETE" ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 备份重要文件
backup_important_files() {
    log_step "备份重要文件"
    
    BACKUP_DIR="/tmp/starbucks_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份部署日志
    if [[ -f "$DEPLOY_HOME/logs/deploy.log" ]]; then
        cp "$DEPLOY_HOME/logs/deploy.log" "$BACKUP_DIR/"
        log_info "部署日志已备份到: $BACKUP_DIR/deploy.log"
    fi
    
    # 备份系统服务状态
    systemctl status starbucks-bypass > "$BACKUP_DIR/service_status.txt" 2>/dev/null || true
    
    # 备份Nginx配置
    if [[ -f "/etc/nginx/sites-available/starbucks-bypass" ]]; then
        cp "/etc/nginx/sites-available/starbucks-bypass" "$BACKUP_DIR/"
    fi
    
    # 备份systemd服务文件
    if [[ -f "/etc/systemd/system/starbucks-bypass.service" ]]; then
        cp "/etc/systemd/system/starbucks-bypass.service" "$BACKUP_DIR/"
    fi
    
    log_info "重要文件备份完成: $BACKUP_DIR"
}

# 停止用户进程
stop_user_processes() {
    log_step "停止用户进程"
    
    # 获取用户进程列表
    USER_PIDS=$(pgrep -u "$DEPLOY_USER" 2>/dev/null || true)
    
    if [[ -n "$USER_PIDS" ]]; then
        log_info "发现用户进程: $USER_PIDS"
        
        # 优雅停止进程
        pkill -TERM -u "$DEPLOY_USER" 2>/dev/null || true
        sleep 3
        
        # 强制停止剩余进程
        pkill -KILL -u "$DEPLOY_USER" 2>/dev/null || true
        sleep 1
        
        log_info "用户进程已停止"
    else
        log_info "未发现用户进程"
    fi
}

# 删除用户账户
delete_user_account() {
    log_step "删除用户账户"
    
    # 删除用户和家目录
    userdel -r "$DEPLOY_USER" 2>/dev/null || true
    
    # 确保家目录完全删除
    if [[ -d "$DEPLOY_HOME" ]]; then
        rm -rf "$DEPLOY_HOME"
        log_info "强制删除用户目录: $DEPLOY_HOME"
    fi
    
    log_info "用户账户已删除"
}

# 删除sudo配置
delete_sudo_config() {
    log_step "删除sudo配置"
    
    if [[ -f "/etc/sudoers.d/$DEPLOY_USER" ]]; then
        rm -f "/etc/sudoers.d/$DEPLOY_USER"
        log_info "sudo配置文件已删除"
    fi
}

# 清理系统日志
clean_system_logs() {
    log_step "清理系统日志"
    
    # 清理auth.log中的用户记录
    if [[ -f "/var/log/auth.log" ]]; then
        sed -i "/$DEPLOY_USER/d" /var/log/auth.log 2>/dev/null || true
    fi
    
    # 清理syslog中的用户记录
    if [[ -f "/var/log/syslog" ]]; then
        sed -i "/$DEPLOY_USER/d" /var/log/syslog 2>/dev/null || true
    fi
    
    # 清理wtmp和utmp记录
    if command -v utmpdump &> /dev/null; then
        utmpdump /var/log/wtmp | grep -v "$DEPLOY_USER" | utmpdump -r > /tmp/wtmp.new 2>/dev/null || true
        if [[ -f "/tmp/wtmp.new" ]]; then
            mv /tmp/wtmp.new /var/log/wtmp
        fi
    fi
    
    log_info "系统日志已清理"
}

# 清理命令历史
clean_command_history() {
    log_step "清理命令历史"
    
    # 清理bash历史
    if [[ -f "$DEPLOY_HOME/.bash_history" ]]; then
        rm -f "$DEPLOY_HOME/.bash_history"
    fi
    
    # 清理当前会话历史
    history -c 2>/dev/null || true
    
    log_info "命令历史已清理"
}

# 完全删除项目相关文件
clean_project_completely() {
    log_step "完全清理项目相关文件"

    # 停止相关服务
    systemctl stop starbucks-bypass 2>/dev/null || true
    systemctl disable starbucks-bypass 2>/dev/null || true
    log_info "已停止并禁用starbucks-bypass服务"

    # 删除用户项目目录
    USER_PROJECT_DIR="$DEPLOY_HOME/starbucks"
    if [[ -d "$USER_PROJECT_DIR" ]]; then
        rm -rf "$USER_PROJECT_DIR"
        log_info "用户项目目录已删除: $USER_PROJECT_DIR"
    fi

    # 删除系统项目目录
    if [[ -d "$PROJECT_DIR" ]]; then
        rm -rf "$PROJECT_DIR"
        log_info "系统项目目录已删除: $PROJECT_DIR"
    fi

    # 删除systemd服务文件
    if [[ -f "/etc/systemd/system/starbucks-bypass.service" ]]; then
        rm -f "/etc/systemd/system/starbucks-bypass.service"
        log_info "systemd服务文件已删除"
    fi

    # 删除Nginx配置
    if [[ -f "/etc/nginx/sites-available/starbucks-bypass" ]]; then
        rm -f "/etc/nginx/sites-available/starbucks-bypass"
        log_info "Nginx配置文件已删除"
    fi

    if [[ -f "/etc/nginx/sites-enabled/starbucks-bypass" ]]; then
        rm -f "/etc/nginx/sites-enabled/starbucks-bypass"
        log_info "Nginx站点链接已删除"
    fi

    # 删除日志文件
    if [[ -d "/var/log/starbucks-bypass" ]]; then
        rm -rf "/var/log/starbucks-bypass"
        log_info "应用日志目录已删除"
    fi

    # 删除临时文件
    rm -rf "/tmp/starbucks*" 2>/dev/null || true
    rm -rf "/var/tmp/starbucks*" 2>/dev/null || true
    log_info "临时文件已清理"

    # 删除Python虚拟环境
    if [[ -d "/opt/starbucks-venv" ]]; then
        rm -rf "/opt/starbucks-venv"
        log_info "Python虚拟环境已删除"
    fi

    # 重新加载systemd
    systemctl daemon-reload 2>/dev/null || true

    # 重启Nginx
    systemctl restart nginx 2>/dev/null || true

    log_info "项目相关文件已完全清除，不留痕迹"
}

# 清理网络连接
clean_network_connections() {
    log_step "清理网络连接"
    
    # 清理可能的SSH连接
    who | grep "$DEPLOY_USER" | awk '{print $2}' | xargs -I {} pkill -f "pts/{}" 2>/dev/null || true
    
    log_info "网络连接已清理"
}

# 验证删除结果
verify_deletion() {
    log_step "验证删除结果"
    
    local errors=0
    
    # 检查用户是否存在
    if id "$DEPLOY_USER" &>/dev/null; then
        log_error "用户 $DEPLOY_USER 仍然存在"
        ((errors++))
    fi
    
    # 检查家目录是否存在
    if [[ -d "$DEPLOY_HOME" ]]; then
        log_error "用户目录 $DEPLOY_HOME 仍然存在"
        ((errors++))
    fi
    
    # 检查sudo配置是否存在
    if [[ -f "/etc/sudoers.d/$DEPLOY_USER" ]]; then
        log_error "sudo配置文件仍然存在"
        ((errors++))
    fi
    
    # 检查进程是否存在
    if pgrep -u "$DEPLOY_USER" &>/dev/null; then
        log_error "用户进程仍在运行"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_info "用户删除验证通过"
        return 0
    else
        log_error "用户删除验证失败，发现 $errors 个问题"
        return 1
    fi
}

# 显示删除结果
show_deletion_result() {
    log_step "删除操作完成"
    
    echo ""
    echo "=========================================="
    echo "用户删除完成"
    echo "=========================================="
    echo ""
    echo "已完全删除内容："
    echo "完成 用户账户: $DEPLOY_USER"
    echo "完成 用户目录: $DEPLOY_HOME"
    echo "完成 用户项目目录: $DEPLOY_HOME/starbucks"
    echo "完成 系统项目目录: $PROJECT_DIR"
    echo "完成 sudo配置文件"
    echo "完成 用户进程"
    echo "完成 系统日志记录"
    echo "完成 命令历史记录"
    echo "完成 systemd服务文件"
    echo "完成 Nginx配置文件"
    echo "完成 应用日志文件"
    echo "完成 临时文件"
    echo "完成 Python虚拟环境"
    echo "完成 网络连接记录"
    echo ""
    echo "保留内容："
    echo "保留 防火墙规则"
    echo "保留 系统基础服务 (Nginx, Redis)"
    echo ""
    echo "清理状态："
    echo "  - 项目已完全卸载，不留任何痕迹"
    echo "  - 所有相关文件和配置已清除"
    echo "  - 如需重新部署，请重新执行完整部署流程"
    echo ""
    echo "=========================================="
    echo ""
}

# 主函数
main() {
    log_info "开始删除部署用户"
    
    check_root
    check_user_exists
    confirm_deletion
    backup_important_files
    stop_user_processes
    clean_command_history
    clean_network_connections
    delete_user_account
    delete_sudo_config
    clean_system_logs
    clean_project_completely
    
    if verify_deletion; then
        show_deletion_result
        log_info "部署用户删除完成！"
    else
        log_error "部署用户删除失败，请手动检查"
        exit 1
    fi
}

# 执行主函数
main "$@"
