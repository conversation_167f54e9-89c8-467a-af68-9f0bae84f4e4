# 设备指纹接口测试 - F5 Shape指纹生成

## 🎯 设备指纹接口说明

客户可以通过以下接口获取F5 Shape设备指纹，用于绕过风控系统。

## 🔑 认证信息
```bash
# 客户专用API密钥
API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 服务器地址
SERVER="http://38.150.2.100:8094"
```

## 📱 核心指纹接口

### 1. 批量生成设备指纹（推荐）
```bash
# 生成5个设备指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 5,
    "force_regenerate": false
  }'
```

**参数说明**:
- `device_count`: 生成指纹数量（1-30）
- `force_regenerate`: 是否强制重新生成（true/false）

**响应示例**:
```json
{
  "success": true,
  "message": "成功生成 5 个设备指纹",
  "fingerprints": [
    {
      "x-device-id": "device_0_1722518400",
      "X-XHPAcPXq-z": "static_value",
      "X-XHPAcPXq-g": "base64_encoded_data...",
      "X-XHPAcPXq-e": "base64_encoded_data...",
      "X-XHPAcPXq-f": "base64_encoded_data...",
      "X-XHPAcPXq-d": "encoded_data...",
      "X-XHPAcPXq-c": "base64_encoded_data...",
      "X-XHPAcPXq-b": "encoded_data...",
      "X-XHPAcPXq-a": "base64_encoded_data...",
      "time": "2025-8-1 22:30:00"
    }
  ]
}
```

### 2. 获取单个设备指纹
```bash
# 获取设备0的指纹
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取设备 0 指纹成功",
  "fingerprints": [
    {
      "x-device-id": "device_0_1722518400",
      "X-XHPAcPXq-z": "static_value",
      "X-XHPAcPXq-g": "base64_encoded_data...",
      "X-XHPAcPXq-e": "base64_encoded_data...",
      "X-XHPAcPXq-f": "base64_encoded_data...",
      "X-XHPAcPXq-d": "encoded_data...",
      "X-XHPAcPXq-c": "base64_encoded_data...",
      "X-XHPAcPXq-b": "encoded_data...",
      "X-XHPAcPXq-a": "base64_encoded_data...",
      "time": "2025-8-1 22:30:00"
    }
  ]
}
```

### 3. 查看设备状态
```bash
# 查看所有设备状态
curl -X GET "http://38.150.2.100:8094/api/v1/devices" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取设备列表成功",
  "data": {
    "total_devices": 30,
    "available_devices": 28,
    "banned_devices": 2,
    "devices": [
      {
        "index": 0,
        "status": "available",
        "last_used": "2025-8-1 22:25:00",
        "success_count": 15,
        "fail_count": 1
      }
    ]
  }
}
```

## 🔧 指纹使用方法

### 在HTTP请求中使用指纹
```bash
# 使用获取的指纹访问目标网站
curl -X GET "https://目标网站.com/api/endpoint" \
  -H "x-device-id: device_0_1722518400" \
  -H "X-XHPAcPXq-z: static_value" \
  -H "X-XHPAcPXq-g: base64_encoded_data..." \
  -H "X-XHPAcPXq-e: base64_encoded_data..." \
  -H "X-XHPAcPXq-f: base64_encoded_data..." \
  -H "X-XHPAcPXq-d: encoded_data..." \
  -H "X-XHPAcPXq-c: base64_encoded_data..." \
  -H "X-XHPAcPXq-b: encoded_data..." \
  -H "X-XHPAcPXq-a: base64_encoded_data..." \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```

### Python集成示例
```python
import requests

class F5FingerprintClient:
    def __init__(self, api_key, base_url="http://38.150.2.100:8094"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def get_fingerprints(self, device_count=5, force_regenerate=False):
        """获取设备指纹"""
        url = f"{self.base_url}/api/v1/fingerprint/generate"
        data = {
            "device_count": device_count,
            "force_regenerate": force_regenerate
        }
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()
    
    def get_single_fingerprint(self, device_index):
        """获取单个设备指纹"""
        url = f"{self.base_url}/api/v1/fingerprint/{device_index}"
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def get_device_status(self):
        """获取设备状态"""
        url = f"{self.base_url}/api/v1/devices"
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def use_fingerprint_request(self, fingerprint, target_url, method="GET", data=None):
        """使用指纹发送请求"""
        # 构建请求头
        request_headers = {}
        for key, value in fingerprint.items():
            if key != "time":  # 排除时间字段
                request_headers[key] = value
        
        # 添加标准浏览器头
        request_headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        })
        
        # 发送请求
        if method.upper() == "GET":
            response = requests.get(target_url, headers=request_headers)
        elif method.upper() == "POST":
            response = requests.post(target_url, headers=request_headers, json=data)
        else:
            response = requests.request(method, target_url, headers=request_headers, json=data)
        
        return response

# 使用示例
client = F5FingerprintClient("SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS")

# 获取5个指纹
result = client.get_fingerprints(5)
if result['success']:
    fingerprints = result['fingerprints']
    print(f"成功获取 {len(fingerprints)} 个指纹")
    
    # 使用第一个指纹访问目标网站
    first_fingerprint = fingerprints[0]
    response = client.use_fingerprint_request(
        first_fingerprint, 
        "https://目标网站.com/api/test"
    )
    print(f"请求状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
```

## 🧪 测试场景

### 场景1：基础指纹获取测试
```bash
# 1. 获取5个指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 5}'

# 2. 查看设备状态
curl -X GET "http://38.150.2.100:8094/api/v1/devices" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 3. 获取单个指纹
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
```

### 场景2：大批量指纹生成
```bash
# 生成30个指纹（最大数量）
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 30, "force_regenerate": true}'
```

### 场景3：指纹有效性验证
```bash
# 1. 获取指纹
FINGERPRINT_RESPONSE=$(curl -s -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 1}')

echo "指纹响应: $FINGERPRINT_RESPONSE"

# 2. 提取指纹字段（需要解析JSON）
# 客户可以使用jq工具解析JSON并提取指纹字段
```

## 📊 指纹字段说明

### F5 Shape指纹字段
- **x-device-id**: 设备唯一标识符
- **X-XHPAcPXq-z**: 静态值标识
- **X-XHPAcPXq-g**: 设备特征编码
- **X-XHPAcPXq-e**: 环境信息编码
- **X-XHPAcPXq-f**: 功能特征编码
- **X-XHPAcPXq-d**: 动态数据编码
- **X-XHPAcPXq-c**: 配置信息编码
- **X-XHPAcPXq-b**: 行为特征编码
- **X-XHPAcPXq-a**: 算法验证编码
- **time**: 生成时间戳

### 指纹特点
- **唯一性**: 每个设备指纹都是唯一的
- **有效性**: 指纹可以有效绕过F5 Shape检测
- **稳定性**: 指纹在一定时间内保持稳定
- **真实性**: 基于真实设备特征生成

## ⚠️ 使用注意事项

### 指纹使用建议
1. **合理分配**: 不要过度使用单个指纹
2. **轮换使用**: 建议轮换使用不同指纹
3. **监控状态**: 定期检查设备状态
4. **及时更新**: 如指纹被封，及时获取新指纹

### 错误处理
- **400错误**: 参数错误，检查device_count范围
- **401错误**: API密钥无效
- **404错误**: 设备不存在
- **500错误**: 服务器内部错误，联系技术支持

## 🎯 客户集成建议

### 集成步骤
1. **获取指纹**: 调用生成接口获取指纹
2. **解析响应**: 提取指纹字段
3. **构建请求**: 将指纹添加到HTTP头
4. **发送请求**: 访问目标网站
5. **监控结果**: 检查请求是否成功

### 最佳实践
- 批量获取指纹提高效率
- 缓存指纹避免重复请求
- 监控指纹使用情况
- 建立指纹轮换机制

---

**技术支持**:
- API文档: http://38.150.2.100:8094/docs
- 7x24小时技术支持
- 指纹有效率保证 > 90%
