#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控和告警模块
作者：YINGAshadow
创建时间：2025-7-29
功能：系统性能监控、告警通知、指标收集
"""

import asyncio
import json
import time
import psutil
import aiohttp
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

from .logger import setup_logger


# 日志记录器
logger = setup_logger(__name__)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_sent: int
    network_recv: int
    active_connections: int


@dataclass
class ApiMetrics:
    """API指标"""
    timestamp: float
    total_requests: int
    success_requests: int
    failed_requests: int
    avg_response_time: float
    active_devices: int
    success_rate: float


@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    alert_type: str
    level: str  # INFO, WARNING, ERROR, CRITICAL
    message: str
    timestamp: float
    resolved: bool = False


class MonitorBackendClient:
    """监控后台客户端"""

    def __init__(self):
        """初始化监控后台客户端"""
        self.monitor_url = os.getenv('MONITOR_BACKEND_URL', 'http://localhost:9000')
        self.monitor_token = os.getenv('MONITOR_BACKEND_TOKEN', '')
        self.session = None
        self.enabled = os.getenv('MONITOR_BACKEND_ENABLED', 'true').lower() == 'true'

    async def init_session(self):
        """初始化HTTP会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None

    async def send_log(self, log_data: Dict):
        """发送日志到监控后台"""
        if not self.enabled or not self.monitor_token:
            return

        try:
            await self.init_session()

            headers = {
                'Authorization': f'Bearer {self.monitor_token}',
                'Content-Type': 'application/json'
            }

            async with self.session.post(
                f'{self.monitor_url}/api/logs/record',
                json=log_data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status == 200:
                    logger.debug("日志发送到监控后台成功")
                else:
                    logger.warning(f"监控后台响应异常: {response.status}")

        except Exception as e:
            logger.error(f"发送日志到监控后台失败: {str(e)}")


class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        """初始化指标收集器"""
        self.system_metrics: List[SystemMetrics] = []
        self.api_metrics: List[ApiMetrics] = []
        self.alerts: List[Alert] = []

        # 指标保留时间（秒）
        self.retention_period = 24 * 3600  # 24小时

        # API统计
        self.api_stats = {
            "total_requests": 0,
            "success_requests": 0,
            "failed_requests": 0,
            "response_times": [],
            "start_time": time.time()
        }

        # 监控后台客户端
        self.monitor_client = MonitorBackendClient()
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络统计
            network = psutil.net_io_counters()
            network_sent = network.bytes_sent
            network_recv = network.bytes_recv
            
            # 活跃连接数
            connections = psutil.net_connections()
            active_connections = len([conn for conn in connections if conn.status == 'ESTABLISHED'])
            
            metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_sent=network_sent,
                network_recv=network_recv,
                active_connections=active_connections
            )
            
            self.system_metrics.append(metrics)
            self._cleanup_old_metrics()
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {str(e)}")
            return None
    
    def record_api_request(self, success: bool, response_time: float):
        """记录API请求"""
        self.api_stats["total_requests"] += 1

        if success:
            self.api_stats["success_requests"] += 1
        else:
            self.api_stats["failed_requests"] += 1

        self.api_stats["response_times"].append(response_time)

        # 只保留最近1000个响应时间
        if len(self.api_stats["response_times"]) > 1000:
            self.api_stats["response_times"] = self.api_stats["response_times"][-1000:]

    async def record_customer_request(self, client_ip: str, customer_id: str,
                                    endpoint: str, method: str, headers: Dict,
                                    request_body: str, response_status: int,
                                    response_body: str, response_time: float,
                                    user_agent: str):
        """记录客户请求到监控后台"""
        try:
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "client_ip": client_ip,
                "customer_id": customer_id,
                "api_endpoint": endpoint,
                "request_method": method,
                "request_headers": headers,
                "request_body": request_body,
                "response_status": response_status,
                "response_body": response_body,
                "response_time": response_time,
                "user_agent": user_agent
            }

            # 发送到监控后台
            await self.monitor_client.send_log(log_data)

        except Exception as e:
            logger.error(f"记录客户请求失败: {str(e)}")

    async def record_all_requests(self, client_ip: str, customer_id: str, user_type: str,
                                endpoint: str, method: str, headers: Dict,
                                request_body: str, response_status: int,
                                response_body: str, response_time: float,
                                user_agent: str):
        """记录所有API请求到监控后台"""
        try:
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "client_ip": client_ip,
                "customer_id": customer_id,
                "user_type": user_type,
                "api_endpoint": endpoint,
                "request_method": method,
                "request_headers": headers,
                "request_body": request_body,
                "response_status": response_status,
                "response_body": response_body,
                "response_time": response_time,
                "user_agent": user_agent
            }

            # 发送到监控后台
            await self.monitor_client.send_log(log_data)

        except Exception as e:
            logger.error(f"记录API请求失败: {str(e)}")
    
    def get_api_metrics(self, active_devices: int = 0) -> ApiMetrics:
        """获取API指标"""
        total_requests = self.api_stats["total_requests"]
        success_requests = self.api_stats["success_requests"]
        failed_requests = self.api_stats["failed_requests"]
        
        # 计算平均响应时间
        response_times = self.api_stats["response_times"]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 计算成功率
        success_rate = success_requests / total_requests if total_requests > 0 else 0
        
        metrics = ApiMetrics(
            timestamp=time.time(),
            total_requests=total_requests,
            success_requests=success_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            active_devices=active_devices,
            success_rate=success_rate
        )
        
        self.api_metrics.append(metrics)
        self._cleanup_old_metrics()
        
        return metrics
    
    def _cleanup_old_metrics(self):
        """清理过期指标"""
        current_time = time.time()
        cutoff_time = current_time - self.retention_period
        
        # 清理系统指标
        self.system_metrics = [
            metric for metric in self.system_metrics
            if metric.timestamp > cutoff_time
        ]
        
        # 清理API指标
        self.api_metrics = [
            metric for metric in self.api_metrics
            if metric.timestamp > cutoff_time
        ]
    
    def get_metrics_summary(self) -> dict:
        """获取指标摘要"""
        current_time = time.time()
        
        # 最新系统指标
        latest_system = self.system_metrics[-1] if self.system_metrics else None
        
        # 最新API指标
        latest_api = self.api_metrics[-1] if self.api_metrics else None
        
        # 运行时间
        uptime = current_time - self.api_stats["start_time"]
        
        return {
            "timestamp": current_time,
            "uptime_seconds": uptime,
            "system_metrics": asdict(latest_system) if latest_system else None,
            "api_metrics": asdict(latest_api) if latest_api else None,
            "total_alerts": len(self.alerts),
            "unresolved_alerts": len([alert for alert in self.alerts if not alert.resolved])
        }


class AlertManager:
    """告警管理器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        """初始化告警管理器"""
        self.metrics_collector = metrics_collector
        self.alert_rules = self._load_alert_rules()
        self.notification_channels = []
    
    def _load_alert_rules(self) -> dict:
        """加载告警规则"""
        return {
            "cpu_high": {"threshold": 80, "level": "WARNING"},
            "cpu_critical": {"threshold": 95, "level": "CRITICAL"},
            "memory_high": {"threshold": 85, "level": "WARNING"},
            "memory_critical": {"threshold": 95, "level": "CRITICAL"},
            "disk_high": {"threshold": 90, "level": "WARNING"},
            "disk_critical": {"threshold": 95, "level": "CRITICAL"},
            "success_rate_low": {"threshold": 0.8, "level": "WARNING"},
            "success_rate_critical": {"threshold": 0.5, "level": "CRITICAL"},
            "response_time_high": {"threshold": 1000, "level": "WARNING"},  # 毫秒
            "response_time_critical": {"threshold": 5000, "level": "CRITICAL"}
        }
    
    def check_alerts(self):
        """检查告警条件"""
        if not self.metrics_collector.system_metrics:
            return
        
        latest_system = self.metrics_collector.system_metrics[-1]
        latest_api = self.metrics_collector.api_metrics[-1] if self.metrics_collector.api_metrics else None
        
        # 检查系统指标告警
        self._check_system_alerts(latest_system)
        
        # 检查API指标告警
        if latest_api:
            self._check_api_alerts(latest_api)
    
    def _check_system_alerts(self, metrics: SystemMetrics):
        """检查系统指标告警"""
        # CPU告警
        if metrics.cpu_percent >= self.alert_rules["cpu_critical"]["threshold"]:
            self._create_alert(
                "cpu_critical",
                "CRITICAL",
                f"CPU使用率过高: {metrics.cpu_percent:.1f}%"
            )
        elif metrics.cpu_percent >= self.alert_rules["cpu_high"]["threshold"]:
            self._create_alert(
                "cpu_high",
                "WARNING",
                f"CPU使用率较高: {metrics.cpu_percent:.1f}%"
            )
        
        # 内存告警
        if metrics.memory_percent >= self.alert_rules["memory_critical"]["threshold"]:
            self._create_alert(
                "memory_critical",
                "CRITICAL",
                f"内存使用率过高: {metrics.memory_percent:.1f}%"
            )
        elif metrics.memory_percent >= self.alert_rules["memory_high"]["threshold"]:
            self._create_alert(
                "memory_high",
                "WARNING",
                f"内存使用率较高: {metrics.memory_percent:.1f}%"
            )
        
        # 磁盘告警
        if metrics.disk_percent >= self.alert_rules["disk_critical"]["threshold"]:
            self._create_alert(
                "disk_critical",
                "CRITICAL",
                f"磁盘使用率过高: {metrics.disk_percent:.1f}%"
            )
        elif metrics.disk_percent >= self.alert_rules["disk_high"]["threshold"]:
            self._create_alert(
                "disk_high",
                "WARNING",
                f"磁盘使用率较高: {metrics.disk_percent:.1f}%"
            )
    
    def _check_api_alerts(self, metrics: ApiMetrics):
        """检查API指标告警"""
        # 成功率告警
        if metrics.success_rate <= self.alert_rules["success_rate_critical"]["threshold"]:
            self._create_alert(
                "success_rate_critical",
                "CRITICAL",
                f"API成功率过低: {metrics.success_rate:.1%}"
            )
        elif metrics.success_rate <= self.alert_rules["success_rate_low"]["threshold"]:
            self._create_alert(
                "success_rate_low",
                "WARNING",
                f"API成功率较低: {metrics.success_rate:.1%}"
            )
        
        # 响应时间告警
        avg_response_ms = metrics.avg_response_time * 1000
        if avg_response_ms >= self.alert_rules["response_time_critical"]["threshold"]:
            self._create_alert(
                "response_time_critical",
                "CRITICAL",
                f"API响应时间过长: {avg_response_ms:.0f}ms"
            )
        elif avg_response_ms >= self.alert_rules["response_time_high"]["threshold"]:
            self._create_alert(
                "response_time_high",
                "WARNING",
                f"API响应时间较长: {avg_response_ms:.0f}ms"
            )
    
    def _create_alert(self, alert_type: str, level: str, message: str):
        """创建告警"""
        alert_id = f"{alert_type}_{int(time.time())}"
        
        # 检查是否已存在相同类型的未解决告警
        existing_alerts = [
            alert for alert in self.metrics_collector.alerts
            if alert.alert_type == alert_type and not alert.resolved
        ]
        
        if existing_alerts:
            # 更新现有告警的时间戳
            existing_alerts[-1].timestamp = time.time()
            return
        
        alert = Alert(
            alert_id=alert_id,
            alert_type=alert_type,
            level=level,
            message=message,
            timestamp=time.time()
        )
        
        self.metrics_collector.alerts.append(alert)
        logger.warning(f"告警触发: [{level}] {message}")
        
        # 发送通知
        self._send_notification(alert)
    
    def _send_notification(self, alert: Alert):
        """发送告警通知"""
        notification_message = f"[{alert.level}] {alert.message}"
        
        # 记录到日志
        if alert.level == "CRITICAL":
            logger.critical(notification_message)
        elif alert.level == "WARNING":
            logger.warning(notification_message)
        else:
            logger.info(notification_message)
    
    def resolve_alert(self, alert_id: str):
        """解决告警"""
        for alert in self.metrics_collector.alerts:
            if alert.alert_id == alert_id:
                alert.resolved = True
                logger.info(f"告警已解决: {alert.message}")
                break
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self.metrics_collector.alerts if not alert.resolved]


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        """初始化系统监控器"""
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager(self.metrics_collector)
        self.monitoring_task = None
        self.is_running = False
    
    async def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self.is_running:
            logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        logger.info(f"开始系统监控，检查间隔: {interval}秒")
        
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(interval)
        )
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        # 关闭监控后台客户端会话
        await self.metrics_collector.monitor_client.close_session()
        logger.info("系统监控已停止")
    
    async def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.is_running:
            try:
                # 收集系统指标
                self.metrics_collector.collect_system_metrics()
                
                # 检查告警
                self.alert_manager.check_alerts()
                
                # 等待下次检查
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}")
                await asyncio.sleep(interval)
    
    def get_status(self) -> dict:
        """获取监控状态"""
        return {
            "is_running": self.is_running,
            "metrics_summary": self.metrics_collector.get_metrics_summary(),
            "active_alerts": len(self.alert_manager.get_active_alerts())
        }


# 全局监控器实例
system_monitor = SystemMonitor()
