# 代码开发规范

## 1. 总体原则

### 1.1 代码质量要求
- 代码必须具备高可读性和可维护性
- 严格遵循PEP 8 Python编码规范
- 所有函数和类必须有详细的文档字符串
- 代码注释必须清晰明确，解释复杂逻辑

### 1.2 禁止事项
- 严禁在Python和Shell脚本中使用emoji表情符号
- 严禁使用任何特殊符号作为状态标识（如✓、✗、●、○等）
- 所有输出必须使用纯文字，不得使用任何图形符号
- 禁止使用硬编码的敏感信息
- 禁止使用不安全的随机数生成器
- 禁止在生产环境中使用调试代码

### 1.3 完整性要求
- **严禁不完整的实现**：所有功能必须完整实现，不允许空函数或占位符
- **严禁不可用的代码**：所有代码必须经过测试验证，确保可正常运行
- **严禁纯模拟实现**：所有功能必须是真实可用的，不允许仅返回模拟数据
- **严禁测试性质的代码**：生产代码必须是完整的业务实现，不是测试或演示
- **必须真实可部署**：系统必须能在真实环境中部署并正常工作
- **必须真实可对接**：API接口必须能与真实的外部系统对接使用

### 1.4 检验排除规则
- **docs文件夹**：完全排除docs目录下的所有文件检验，不受任何代码规范约束
- **Markdown文件**：所有.md文件不受任何代码规范检验，可以包含任何格式和内容
- **文档内容**：文档可以使用emoji、特殊格式等，完全不受代码规范限制

## 2. Python开发规范

### 2.1 文件结构
```
项目根目录/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   ├── utils/             # 工具模块
│   ├── api/               # API接口模块
│   └── config/            # 配置模块
├── scripts/               # Shell脚本目录
├── tests/                 # 测试目录
├── docs/                  # 文档目录
└── requirements.txt       # 依赖文件
```

### 2.2 命名规范
- 文件名：使用小写字母和下划线，如 `device_fingerprint.py`
- 类名：使用驼峰命名法，如 `DeviceFingerprint`
- 函数名：使用小写字母和下划线，如 `generate_fingerprint`
- 变量名：使用小写字母和下划线，如 `device_id`
- 常量名：使用大写字母和下划线，如 `MAX_RETRY_COUNT`

### 2.3 代码格式
- 使用4个空格缩进，禁止使用Tab
- 每行代码长度不超过88个字符
- 函数之间空两行，类之间空两行
- 导入语句按标准库、第三方库、本地模块的顺序排列

### 2.4 异常处理
- 必须使用具体的异常类型，避免使用裸露的except
- 异常信息必须包含足够的上下文信息
- 关键操作必须有重试机制

### 2.5 日志规范
- 使用Python标准logging模块
- 日志级别：DEBUG、INFO、WARNING、ERROR、CRITICAL
- 日志格式：时间戳 + 级别 + 模块名 + 消息
- 所有日志消息必须使用中文
- 用户界面显示内容严禁使用英文

### 2.6 显示语言规范
- 所有用户可见的输出信息必须使用中文
- 控制台打印信息、错误提示、状态信息均使用中文
- API响应消息使用中文
- 异常信息描述使用中文
- 状态标识必须使用纯文字：成功、失败、完成、进行中、警告、错误等
- 严禁使用任何符号作为状态标识

## 3. Shell脚本规范

### 3.1 环境说明
- Shell脚本仅适用于Linux Ubuntu服务器环境
- 不支持Windows环境，Windows用户请使用WSL或虚拟机
- 目标服务器系统：Ubuntu 20.04 LTS及以上版本
- 默认Shell：/bin/bash

### 3.2 显示语言规范
- 所有脚本输出信息必须使用中文
- 控制台显示内容严禁使用英文
- 错误信息、提示信息、日志信息均使用中文
- 变量名和函数名可使用英文，但注释和输出必须中文
- 状态标识必须使用纯文字：成功、失败、完成、进行中、警告、错误等
- 严禁使用任何符号作为状态标识

### 3.3 脚本头部
```bash
#!/bin/bash
# 脚本名称：script_name.sh
# 功能描述：脚本功能说明
# 作者：YINGAshadow
# 创建时间：YYYY-MM-DD
# 修改时间：YYYY-MM-DD
# 目标环境：Linux Ubuntu Server
```

### 3.4 变量规范
- 全局变量使用大写字母，如 `CONFIG_FILE`
- 局部变量使用小写字母，如 `local_var`
- 只读变量使用readonly声明
- 环境变量必须有默认值

### 3.5 错误处理
- 脚本开头添加 `set -euo pipefail`
- 重要命令必须检查返回值
- 提供清晰的错误信息

### 3.6 函数规范
- 函数名使用小写字母和下划线
- 函数必须有注释说明功能、参数和返回值
- 使用local声明局部变量

## 4. 安全规范

### 4.1 数据安全
- 敏感数据必须加密存储
- 使用环境变量或配置文件管理敏感信息
- 定期轮换密钥和令牌

### 4.2 网络安全
- 使用HTTPS进行数据传输
- 实现请求频率限制
- 添加请求超时设置

### 4.3 输入验证
- 所有外部输入必须验证
- 使用参数化查询防止注入攻击
- 对用户输入进行清理和转义

## 5. 性能规范

### 5.1 并发处理
- 使用异步编程提高性能
- 合理设置线程池大小
- 避免阻塞操作

### 5.2 资源管理
- 及时释放文件句柄和网络连接
- 使用上下文管理器管理资源
- 监控内存使用情况

## 6. 测试规范

### 6.1 单元测试
- 测试覆盖率不低于80%
- 使用pytest框架编写测试
- 测试文件命名为 `test_*.py`

### 6.2 集成测试
- 测试关键业务流程
- 模拟真实环境进行测试
- 验证系统集成点

## 7. 文档规范

### 7.1 代码文档
- 所有公共函数必须有docstring
- 使用Google风格的docstring格式
- 包含参数说明、返回值和异常信息

### 7.2 项目文档
- README.md包含项目介绍和使用说明
- API文档使用Swagger或类似工具生成
- 部署文档详细说明环境要求和部署步骤

## 8. 操作记录规范

### 8.1 回顾文档要求
- 每次完成任何操作都必须在项目同级的docs目录下创建回顾文档
- 文档命名格式：回顾_操作内容_YYYYMMDD.md
- 必须记录操作的详细过程、遇到的问题和解决方案
- 回顾文档必须使用中文编写

### 8.2 回顾文档内容结构
- 操作概述：简要说明本次操作的目标和内容
- 操作步骤：详细记录每一步的具体操作
- 遇到问题：记录操作过程中遇到的所有问题
- 解决方案：详细说明问题的解决方法
- 操作结果：总结操作的最终结果和效果
- 经验总结：记录本次操作的经验和教训

### 8.3 文档管理
- 所有回顾文档统一存放在同级docs目录
- 按日期和功能模块分类整理
- 定期整理和归档历史文档

## 9. 部署规范

### 9.1 环境配置
- 目标部署环境：Linux Ubuntu Server（推荐20.04 LTS）
- 使用Python虚拟环境隔离依赖
- 开发、测试、生产环境严格分离
- 配置文件与代码分离
- 不支持Windows服务器部署

### 9.2 监控告警
- 实现应用性能监控
- 设置关键指标告警
- 记录详细的操作日志

## 10. 代码审查规范

### 10.1 审查要点
- 代码逻辑正确性
- 安全漏洞检查
- 性能优化建议
- 代码规范遵循情况

### 10.2 审查流程
- 开发者自检
- 同行代码审查
- 技术负责人最终审查
- 通过后方可合并
