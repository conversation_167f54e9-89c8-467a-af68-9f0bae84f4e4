#!/bin/bash

# 星巴克F5 Shape绕过系统部署验证脚本
# 作者: YINGAshadow
# 用途: 验证系统部署是否完整可用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "系统必须是Linux Ubuntu"
        exit 1
    fi
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    log_success "Python版本: $python_version"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    required_dirs=(
        "src"
        "src/core"
        "src/api"
        "src/utils"
        "src/config"
        "scripts"
        "tests"
        "data"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_error "缺少目录: $dir"
            exit 1
        fi
    done
    
    required_files=(
        "src/core/f5_shape_generator.py"
        "src/core/device_manager.py"
        "src/api/main.py"
        "src/utils/bypass_tester.py"
        "src/utils/auth.py"
        "src/utils/monitor.py"
        "src/config/settings.py"
        "requirements.txt"
        "abcd.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少文件: $file"
            exit 1
        fi
    done
    
    log_success "项目结构检查通过"
}

# 检查虚拟环境
check_virtual_env() {
    log_info "检查Python虚拟环境..."
    
    if [[ ! -d "venv" ]]; then
        log_warning "虚拟环境不存在，正在创建..."
        python3 -m venv venv
        log_success "虚拟环境创建完成"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查依赖
    log_info "检查Python依赖..."
    pip install -r requirements.txt
    
    log_success "虚拟环境检查通过"
}

# 检查配置文件
check_configuration() {
    log_info "检查配置文件..."
    
    # 检查设置文件
    python3 -c "
import sys
sys.path.append('src')
from config.settings import settings
print(f'最大设备数: {settings.MAX_DEVICES}')
print(f'设备池大小: {settings.DEVICE_POOL_SIZE}')
print(f'默认测试端点: {settings.DEFAULT_TEST_ENDPOINT}')
"
    
    log_success "配置文件检查通过"
}

# 检查核心模块
check_core_modules() {
    log_info "检查核心模块..."
    
    # 测试F5 Shape生成器
    python3 -c "
import sys
sys.path.append('src')
from core.f5_shape_generator import F5ShapeGenerator
generator = F5ShapeGenerator()
fingerprint = generator.generate_fingerprint()
print(f'指纹生成测试: {len(fingerprint)} 个字段')
"
    
    # 测试设备管理器
    python3 -c "
import sys
sys.path.append('src')
from core.device_manager import DeviceManager
import asyncio

async def test_device_manager():
    manager = DeviceManager()
    await manager.initialize()
    devices = manager.get_device_list()
    print(f'设备管理器测试: {len(devices)} 个设备')

asyncio.run(test_device_manager())
"
    
    log_success "核心模块检查通过"
}

# 检查API服务
check_api_service() {
    log_info "检查API服务..."
    
    # 启动API服务（后台）
    source venv/bin/activate
    cd src
    python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 &
    API_PID=$!
    cd ..
    
    # 等待服务启动
    sleep 5
    
    # 测试API端点
    if curl -s http://localhost:8000/ | grep -q "success"; then
        log_success "API服务启动成功"
    else
        log_error "API服务启动失败"
        kill $API_PID 2>/dev/null || true
        exit 1
    fi
    
    # 停止API服务
    kill $API_PID 2>/dev/null || true
    sleep 2
    
    log_success "API服务检查通过"
}

# 检查数据文件
check_data_files() {
    log_info "检查数据文件..."
    
    # 检查abcd.txt
    if [[ ! -f "abcd.txt" ]]; then
        log_error "缺少指纹数据文件 abcd.txt"
        exit 1
    fi
    
    line_count=$(wc -l < abcd.txt)
    if [[ $line_count -lt 100 ]]; then
        log_warning "指纹数据文件行数较少: $line_count"
    else
        log_success "指纹数据文件检查通过: $line_count 行"
    fi
}

# 检查systemd服务配置
check_systemd_service() {
    log_info "检查systemd服务配置..."
    
    if [[ -f "/etc/systemd/system/starbucks-bypass.service" ]]; then
        log_success "systemd服务已配置"
        
        # 检查服务状态
        if systemctl is-enabled starbucks-bypass.service &>/dev/null; then
            log_success "服务已启用"
        else
            log_warning "服务未启用"
        fi
    else
        log_warning "systemd服务未配置"
    fi
}

# 检查Nginx配置
check_nginx() {
    log_info "检查Nginx配置..."
    
    if command -v nginx &> /dev/null; then
        log_success "Nginx已安装"
        
        if [[ -f "/etc/nginx/sites-available/starbucks-bypass" ]]; then
            log_success "Nginx站点配置存在"
        else
            log_warning "Nginx站点配置不存在"
        fi
    else
        log_warning "Nginx未安装"
    fi
}

# 性能测试
performance_test() {
    log_info "执行性能测试..."
    
    # 启动API服务
    source venv/bin/activate
    cd src
    python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 &
    API_PID=$!
    cd ..
    
    sleep 5
    
    # 并发测试
    log_info "测试30设备并发..."
    start_time=$(date +%s)
    
    response=$(curl -s -X POST "http://localhost:8000/api/v1/test/bypass" \
        -H "Content-Type: application/json" \
        -d '{"concurrent_count": 30}')
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "30设备并发测试通过，耗时: ${duration}秒"
    else
        log_error "30设备并发测试失败"
    fi
    
    # 停止API服务
    kill $API_PID 2>/dev/null || true
    sleep 2
}

# 主函数
main() {
    log_info "开始星巴克F5 Shape绕过系统部署验证"
    echo "========================================"
    
    check_system
    echo ""
    
    check_project_structure
    echo ""
    
    check_virtual_env
    echo ""
    
    check_configuration
    echo ""
    
    check_core_modules
    echo ""
    
    check_data_files
    echo ""
    
    check_api_service
    echo ""
    
    check_systemd_service
    echo ""
    
    check_nginx
    echo ""
    
    performance_test
    echo ""
    
    log_success "部署验证完成！"
    log_info "系统已准备就绪，可以开始使用"
    echo ""
    log_info "启动命令: ./scripts/deploy.sh"
    log_info "测试命令: ./scripts/test_api.sh"
}

# 执行主函数
main "$@"
