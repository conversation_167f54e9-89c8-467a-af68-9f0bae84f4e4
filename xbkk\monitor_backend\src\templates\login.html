<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星巴克风控绕过系统 - 监控后台登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/login.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-section">
                    <i class="bi bi-shield-check logo-icon"></i>
                    <h2 class="system-title">星巴克风控绕过系统</h2>
                    <p class="system-subtitle">监控后台管理系统</p>
                </div>
            </div>
            
            <div class="login-body-content">
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="bi bi-person-circle"></i>
                            管理员账户
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="username" 
                               name="username" 
                               placeholder="请输入管理员账户"
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="bi bi-lock-fill"></i>
                            登录密码
                        </label>
                        <div class="password-input-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   placeholder="请输入登录密码"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="bi bi-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group remember-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                记住登录状态
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="bi bi-box-arrow-in-right"></i>
                        登录系统
                    </button>
                </form>
                
                <div id="loginAlert" class="alert alert-danger login-alert" style="display: none;">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                    <span id="loginAlertMessage">登录失败，请检查账户和密码</span>
                </div>
            </div>
            
            <div class="login-footer">
                <div class="system-info">
                    <p class="info-text">
                        <i class="bi bi-info-circle"></i>
                        系统版本：v1.0.0 | 企业级监控解决方案
                    </p>
                    <p class="security-notice">
                        <i class="bi bi-shield-exclamation"></i>
                        本系统仅限授权人员使用，所有操作将被记录
                    </p>
                </div>
            </div>
        </div>
        
        <div class="background-animation">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/login.js"></script>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loginData = {
                username: formData.get('username'),
                password: formData.get('password'),
                remember_me: formData.get('rememberMe') === 'on'
            };
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    // 登录成功，保存token并跳转
                    localStorage.setItem('access_token', result.access_token);
                    if (loginData.remember_me) {
                        localStorage.setItem('remember_login', 'true');
                    }
                    window.location.href = '/dashboard';
                } else {
                    // 登录失败，显示错误信息
                    showLoginError(result.message || '登录失败，请检查账户和密码');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                showLoginError('网络连接失败，请稍后重试');
            }
        });
        
        function showLoginError(message) {
            const alertDiv = document.getElementById('loginAlert');
            const messageSpan = document.getElementById('loginAlertMessage');
            
            messageSpan.textContent = message;
            alertDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 3000);
        }
        
        // 检查是否已经登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('access_token');
            if (token) {
                // 验证token是否有效
                fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '/dashboard';
                    } else {
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('remember_login');
                    }
                });
            }
        });
    </script>
</body>
</html>
