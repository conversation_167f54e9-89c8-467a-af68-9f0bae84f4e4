#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星巴克设备指纹风控绕过系统 - 风控绕过分析器
功能：分析F5 Shape指纹特征，评估绕过效果，提供风控绕过分析
作者：YINGAshadow
创建时间：2025-01-31
修改时间：2025-07-31
"""

import json
import time
import hashlib
import statistics
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

from .f5_shape_generator import F5ShapeGenerator
from .f5_analyzer import F5Analyzer
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BypassResult:
    """绕过结果数据类"""
    device_id: str
    fingerprint: str
    bypass_score: float
    risk_level: str
    success_probability: float
    analysis_details: Dict[str, Any]
    timestamp: datetime


class BypassAnalyzer:
    """风控绕过分析器"""
    
    def __init__(self):
        """初始化绕过分析器"""
        self.f5_generator = F5ShapeGenerator()
        self.f5_analyzer = F5Analyzer()
        self.bypass_history = []
        self.success_threshold = 0.85
        self.risk_levels = {
            'low': (0.9, 1.0),
            'medium': (0.7, 0.9),
            'high': (0.5, 0.7),
            'critical': (0.0, 0.5)
        }
        
        logger.info("风控绕过分析器初始化完成")
    
    def analyze_bypass_effectiveness(self, fingerprint: str, device_info: Dict[str, Any]) -> BypassResult:
        """
        分析指纹的风控绕过效果
        
        Args:
            fingerprint: F5 Shape指纹
            device_info: 设备信息
            
        Returns:
            BypassResult: 绕过分析结果
        """
        try:
            # 解析指纹数据
            fingerprint_data = self.f5_analyzer.parse_fingerprint(fingerprint)
            
            # 计算绕过评分
            bypass_score = self._calculate_bypass_score(fingerprint_data)
            
            # 评估风险等级
            risk_level = self._assess_risk_level(bypass_score)
            
            # 计算成功概率
            success_probability = self._calculate_success_probability(
                fingerprint_data, device_info
            )
            
            # 生成详细分析
            analysis_details = self._generate_analysis_details(
                fingerprint_data, device_info, bypass_score
            )
            
            # 创建结果对象
            result = BypassResult(
                device_id=device_info.get('device_id', 'unknown'),
                fingerprint=fingerprint,
                bypass_score=bypass_score,
                risk_level=risk_level,
                success_probability=success_probability,
                analysis_details=analysis_details,
                timestamp=datetime.now()
            )
            
            # 记录到历史
            self.bypass_history.append(result)
            
            logger.info(f"设备 {result.device_id} 绕过分析完成，评分: {bypass_score:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"绕过效果分析失败: {str(e)}")
            raise
    
    def _calculate_bypass_score(self, fingerprint_data: Dict[str, Any]) -> float:
        """
        计算绕过评分
        
        Args:
            fingerprint_data: 指纹数据
            
        Returns:
            float: 绕过评分 (0.0-1.0)
        """
        try:
            score_factors = []
            
            # 1. 指纹复杂度评分 (30%)
            complexity_score = self._evaluate_complexity(fingerprint_data)
            score_factors.append(('complexity', complexity_score, 0.3))
            
            # 2. 熵值评分 (25%)
            entropy_score = self._evaluate_entropy(fingerprint_data)
            score_factors.append(('entropy', entropy_score, 0.25))
            
            # 3. 时间戳相关性评分 (20%)
            timestamp_score = self._evaluate_timestamp_correlation(fingerprint_data)
            score_factors.append(('timestamp', timestamp_score, 0.2))
            
            # 4. 设备特征一致性评分 (15%)
            consistency_score = self._evaluate_device_consistency(fingerprint_data)
            score_factors.append(('consistency', consistency_score, 0.15))
            
            # 5. 反检测特征评分 (10%)
            anti_detection_score = self._evaluate_anti_detection(fingerprint_data)
            score_factors.append(('anti_detection', anti_detection_score, 0.1))
            
            # 计算加权总分
            total_score = sum(score * weight for _, score, weight in score_factors)
            
            # 应用动态调整
            adjusted_score = self._apply_dynamic_adjustments(total_score, fingerprint_data)
            
            # 确保评分在有效范围内
            final_score = max(0.0, min(1.0, adjusted_score))
            
            logger.debug(f"绕过评分计算: {score_factors}, 最终评分: {final_score:.3f}")
            
            return final_score
            
        except Exception as e:
            logger.error(f"绕过评分计算失败: {str(e)}")
            return 0.5  # 返回中等评分作为默认值
    
    def _evaluate_complexity(self, fingerprint_data: Dict[str, Any]) -> float:
        """评估指纹复杂度"""
        try:
            complexity_factors = []
            
            # 检查字段数量
            field_count = len(fingerprint_data.get('fields', {}))
            field_score = min(1.0, field_count / 50)  # 假设50个字段为满分
            complexity_factors.append(field_score)
            
            # 检查数据长度
            total_length = sum(len(str(v)) for v in fingerprint_data.get('fields', {}).values())
            length_score = min(1.0, total_length / 5000)  # 假设5000字符为满分
            complexity_factors.append(length_score)
            
            # 检查嵌套结构
            nested_score = self._evaluate_nested_structure(fingerprint_data)
            complexity_factors.append(nested_score)
            
            return statistics.mean(complexity_factors)
            
        except Exception:
            return 0.5
    
    def _evaluate_entropy(self, fingerprint_data: Dict[str, Any]) -> float:
        """评估指纹熵值"""
        try:
            # 计算整体数据的熵值
            data_string = json.dumps(fingerprint_data, sort_keys=True)
            entropy = self.f5_analyzer.calculate_entropy(data_string)
            
            # 将熵值转换为0-1评分
            # 高熵值表示更好的随机性和绕过效果
            normalized_entropy = min(1.0, entropy / 8.0)  # 假设8为最大熵值
            
            return normalized_entropy
            
        except Exception:
            return 0.5
    
    def _evaluate_timestamp_correlation(self, fingerprint_data: Dict[str, Any]) -> float:
        """评估时间戳相关性"""
        try:
            # 检查时间戳字段的合理性
            timestamp_fields = []
            for key, value in fingerprint_data.get('fields', {}).items():
                if 'time' in key.lower() or 'timestamp' in key.lower():
                    timestamp_fields.append(value)
            
            if not timestamp_fields:
                return 0.8  # 没有时间戳字段，给予较高评分
            
            # 评估时间戳的合理性
            current_time = int(time.time() * 1000)
            reasonable_timestamps = 0
            
            for ts in timestamp_fields:
                try:
                    ts_value = int(ts)
                    # 检查时间戳是否在合理范围内（过去24小时到未来1小时）
                    if current_time - 86400000 <= ts_value <= current_time + 3600000:
                        reasonable_timestamps += 1
                except (ValueError, TypeError):
                    continue
            
            if len(timestamp_fields) == 0:
                return 0.8
            
            return reasonable_timestamps / len(timestamp_fields)
            
        except Exception:
            return 0.5
    
    def _evaluate_device_consistency(self, fingerprint_data: Dict[str, Any]) -> float:
        """评估设备特征一致性"""
        try:
            consistency_score = 0.0
            total_checks = 0
            
            fields = fingerprint_data.get('fields', {})
            
            # 检查屏幕分辨率一致性
            if 'screen_width' in fields and 'screen_height' in fields:
                width = int(fields.get('screen_width', 0))
                height = int(fields.get('screen_height', 0))
                if width > 0 and height > 0:
                    aspect_ratio = width / height
                    # 常见的移动设备宽高比
                    common_ratios = [16/9, 18/9, 19.5/9, 20/9, 4/3]
                    if any(abs(aspect_ratio - ratio) < 0.1 for ratio in common_ratios):
                        consistency_score += 1.0
                total_checks += 1
            
            # 检查用户代理一致性
            if 'user_agent' in fields:
                ua = str(fields['user_agent'])
                if 'Mobile' in ua or 'Android' in ua or 'iPhone' in ua:
                    consistency_score += 1.0
                total_checks += 1
            
            # 检查语言设置一致性
            if 'language' in fields:
                lang = str(fields['language'])
                if lang in ['zh-CN', 'en-US', 'zh-TW', 'en-GB']:
                    consistency_score += 1.0
                total_checks += 1
            
            return consistency_score / total_checks if total_checks > 0 else 0.8
            
        except Exception:
            return 0.5
    
    def _evaluate_anti_detection(self, fingerprint_data: Dict[str, Any]) -> float:
        """评估反检测特征"""
        try:
            anti_detection_score = 0.0
            total_checks = 0
            
            fields = fingerprint_data.get('fields', {})
            
            # 检查是否包含常见的反检测字段
            anti_detection_fields = [
                'webgl_vendor', 'webgl_renderer', 'canvas_fingerprint',
                'audio_fingerprint', 'font_list', 'plugin_list'
            ]
            
            for field in anti_detection_fields:
                if field in fields and fields[field]:
                    anti_detection_score += 1.0
                total_checks += 1
            
            # 检查字段值的多样性
            unique_values = len(set(str(v) for v in fields.values()))
            diversity_score = min(1.0, unique_values / len(fields)) if fields else 0
            
            final_score = (anti_detection_score / total_checks + diversity_score) / 2 if total_checks > 0 else diversity_score
            
            return final_score
            
        except Exception:
            return 0.5
    
    def _evaluate_nested_structure(self, data: Any, depth: int = 0) -> float:
        """评估嵌套结构复杂度"""
        if depth > 5:  # 防止无限递归
            return 1.0
        
        if isinstance(data, dict):
            nested_score = 0.1 * depth  # 每层嵌套增加0.1分
            for value in data.values():
                nested_score += self._evaluate_nested_structure(value, depth + 1)
            return min(1.0, nested_score / len(data)) if data else 0
        elif isinstance(data, list):
            if not data:
                return 0
            nested_score = 0.05 * depth  # 数组嵌套分数较低
            for item in data:
                nested_score += self._evaluate_nested_structure(item, depth + 1)
            return min(1.0, nested_score / len(data))
        else:
            return 0.1 if depth > 0 else 0
    
    def _apply_dynamic_adjustments(self, base_score: float, fingerprint_data: Dict[str, Any]) -> float:
        """应用动态调整"""
        try:
            adjusted_score = base_score
            
            # 根据历史成功率调整
            if len(self.bypass_history) > 10:
                recent_success_rate = self._calculate_recent_success_rate()
                if recent_success_rate > 0.9:
                    adjusted_score *= 1.05  # 最近成功率高，略微提升评分
                elif recent_success_rate < 0.7:
                    adjusted_score *= 0.95  # 最近成功率低，略微降低评分
            
            # 根据指纹唯一性调整
            uniqueness_score = self._calculate_fingerprint_uniqueness(fingerprint_data)
            adjusted_score = (adjusted_score + uniqueness_score) / 2
            
            return adjusted_score
            
        except Exception:
            return base_score
    
    def _calculate_recent_success_rate(self, hours: int = 24) -> float:
        """计算最近的成功率"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_results = [
                r for r in self.bypass_history 
                if r.timestamp >= cutoff_time
            ]
            
            if not recent_results:
                return 0.8  # 默认成功率
            
            successful_results = [
                r for r in recent_results 
                if r.bypass_score >= self.success_threshold
            ]
            
            return len(successful_results) / len(recent_results)
            
        except Exception:
            return 0.8
    
    def _calculate_fingerprint_uniqueness(self, fingerprint_data: Dict[str, Any]) -> float:
        """计算指纹唯一性"""
        try:
            # 简化的唯一性计算
            fingerprint_hash = hashlib.md5(
                json.dumps(fingerprint_data, sort_keys=True).encode()
            ).hexdigest()
            
            # 检查是否与历史指纹重复
            historical_hashes = [
                hashlib.md5(r.fingerprint.encode()).hexdigest()
                for r in self.bypass_history[-100:]  # 只检查最近100个
            ]
            
            if fingerprint_hash in historical_hashes:
                return 0.3  # 重复指纹，唯一性低
            else:
                return 0.9  # 唯一指纹，唯一性高
                
        except Exception:
            return 0.7
    
    def _assess_risk_level(self, bypass_score: float) -> str:
        """评估风险等级"""
        for level, (min_score, max_score) in self.risk_levels.items():
            if min_score <= bypass_score < max_score:
                return level
        return 'critical'
    
    def _calculate_success_probability(self, fingerprint_data: Dict[str, Any], device_info: Dict[str, Any]) -> float:
        """计算成功概率"""
        try:
            # 基础概率基于绕过评分
            base_probability = self._calculate_bypass_score(fingerprint_data)
            
            # 根据设备类型调整
            device_type = device_info.get('device_type', 'unknown')
            if device_type == 'mobile':
                base_probability *= 1.1  # 移动设备成功率稍高
            elif device_type == 'desktop':
                base_probability *= 0.95  # 桌面设备成功率稍低
            
            # 根据历史数据调整
            if len(self.bypass_history) > 5:
                recent_success_rate = self._calculate_recent_success_rate()
                base_probability = (base_probability + recent_success_rate) / 2
            
            return max(0.0, min(1.0, base_probability))
            
        except Exception:
            return 0.75
    
    def _generate_analysis_details(self, fingerprint_data: Dict[str, Any], device_info: Dict[str, Any], bypass_score: float) -> Dict[str, Any]:
        """生成详细分析信息"""
        try:
            return {
                'fingerprint_complexity': self._evaluate_complexity(fingerprint_data),
                'entropy_score': self._evaluate_entropy(fingerprint_data),
                'timestamp_correlation': self._evaluate_timestamp_correlation(fingerprint_data),
                'device_consistency': self._evaluate_device_consistency(fingerprint_data),
                'anti_detection_features': self._evaluate_anti_detection(fingerprint_data),
                'field_count': len(fingerprint_data.get('fields', {})),
                'data_size': len(json.dumps(fingerprint_data)),
                'device_type': device_info.get('device_type', 'unknown'),
                'analysis_timestamp': datetime.now().isoformat(),
                'bypass_recommendation': self._generate_bypass_recommendation(bypass_score)
            }
        except Exception as e:
            logger.error(f"生成分析详情失败: {str(e)}")
            return {'error': str(e)}
    
    def _generate_bypass_recommendation(self, bypass_score: float) -> str:
        """生成绕过建议"""
        if bypass_score >= 0.9:
            return "指纹质量优秀，建议直接使用"
        elif bypass_score >= 0.8:
            return "指纹质量良好，可以使用"
        elif bypass_score >= 0.7:
            return "指纹质量一般，建议优化后使用"
        elif bypass_score >= 0.6:
            return "指纹质量较差，需要重新生成"
        else:
            return "指纹质量很差，不建议使用"
    
    def get_bypass_statistics(self) -> Dict[str, Any]:
        """获取绕过统计信息"""
        try:
            if not self.bypass_history:
                return {
                    'total_attempts': 0,
                    'success_rate': 0.0,
                    'average_score': 0.0,
                    'risk_distribution': {},
                    'recent_trend': 'stable'
                }
            
            total_attempts = len(self.bypass_history)
            successful_attempts = len([r for r in self.bypass_history if r.bypass_score >= self.success_threshold])
            success_rate = successful_attempts / total_attempts
            average_score = statistics.mean([r.bypass_score for r in self.bypass_history])
            
            # 风险等级分布
            risk_distribution = {}
            for result in self.bypass_history:
                risk_level = result.risk_level
                risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
            
            # 最近趋势
            recent_trend = self._analyze_recent_trend()
            
            return {
                'total_attempts': total_attempts,
                'success_rate': success_rate,
                'average_score': average_score,
                'risk_distribution': risk_distribution,
                'recent_trend': recent_trend,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取绕过统计失败: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_recent_trend(self) -> str:
        """分析最近趋势"""
        try:
            if len(self.bypass_history) < 10:
                return 'insufficient_data'
            
            # 取最近10个结果和之前10个结果比较
            recent_scores = [r.bypass_score for r in self.bypass_history[-10:]]
            previous_scores = [r.bypass_score for r in self.bypass_history[-20:-10]]
            
            recent_avg = statistics.mean(recent_scores)
            previous_avg = statistics.mean(previous_scores)
            
            if recent_avg > previous_avg + 0.05:
                return 'improving'
            elif recent_avg < previous_avg - 0.05:
                return 'declining'
            else:
                return 'stable'
                
        except Exception:
            return 'unknown'
