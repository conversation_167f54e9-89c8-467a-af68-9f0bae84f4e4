#!/bin/bash
# 测试运行脚本
# 作者：YINGAshadow
# 创建时间：2025-7-29
# 功能：运行所有测试用例并生成测试报告

# 配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_DIR="$PROJECT_ROOT/tests"
REPORT_DIR="$PROJECT_ROOT/test_reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[测试]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

# 创建报告目录
create_report_dir() {
    if [ ! -d "$REPORT_DIR" ]; then
        mkdir -p "$REPORT_DIR"
        log_info "创建测试报告目录: $REPORT_DIR"
    fi
}

# 检查Python环境
check_python_env() {
    log_test "检查Python环境"
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查虚拟环境
    if [ -d "$PROJECT_ROOT/venv" ]; then
        log_info "激活虚拟环境"
        source "$PROJECT_ROOT/venv/bin/activate"
    else
        log_warning "未找到虚拟环境，使用系统Python"
    fi
}

# 安装测试依赖
install_test_dependencies() {
    log_test "安装测试依赖"
    
    # 检查是否需要安装pytest
    if ! python3 -c "import pytest" 2>/dev/null; then
        log_info "安装pytest"
        pip3 install pytest pytest-html pytest-cov
    fi
    
    # 检查是否需要安装其他测试依赖
    if ! python3 -c "import coverage" 2>/dev/null; then
        log_info "安装coverage"
        pip3 install coverage
    fi
}

# 运行单元测试
run_unit_tests() {
    log_test "运行单元测试"
    
    cd "$PROJECT_ROOT"
    
    # F5生成器测试
    log_info "测试F5指纹生成器"
    python3 -m pytest tests/test_f5_generator.py -v --tb=short \
        --html="$REPORT_DIR/f5_generator_report_$TIMESTAMP.html" \
        --self-contained-html
    
    if [ $? -eq 0 ]; then
        log_info "F5指纹生成器测试通过"
    else
        log_error "F5指纹生成器测试失败"
    fi
    
    # API测试
    log_info "测试API接口"
    python3 -m pytest tests/test_api.py -v --tb=short \
        --html="$REPORT_DIR/api_test_report_$TIMESTAMP.html" \
        --self-contained-html
    
    if [ $? -eq 0 ]; then
        log_info "API接口测试通过"
    else
        log_error "API接口测试失败"
    fi
}

# 运行覆盖率测试
run_coverage_test() {
    log_test "运行代码覆盖率测试"
    
    cd "$PROJECT_ROOT"
    
    # 运行覆盖率测试
    coverage run -m pytest tests/ --tb=short
    
    # 生成覆盖率报告
    coverage report -m > "$REPORT_DIR/coverage_report_$TIMESTAMP.txt"
    coverage html -d "$REPORT_DIR/coverage_html_$TIMESTAMP"
    
    # 显示覆盖率摘要
    log_info "代码覆盖率报告:"
    coverage report --skip-covered
}

# 运行性能测试
run_performance_test() {
    log_test "运行性能测试"
    
    cd "$PROJECT_ROOT"
    
    # 创建性能测试脚本
    cat > "$REPORT_DIR/performance_test_$TIMESTAMP.py" << 'EOF'
#!/usr/bin/env python3
import time
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.f5_shape_generator import F5ShapeGenerator
from src.core.device_manager import DeviceManager

def test_fingerprint_generation_performance():
    """测试指纹生成性能"""
    generator = F5ShapeGenerator()
    
    # 单个指纹生成测试
    start_time = time.time()
    for i in range(100):
        generator.generate_fingerprint(i)
    single_time = time.time() - start_time
    
    print(f"单个指纹生成 (100次): {single_time:.3f}秒")
    print(f"平均时间: {single_time/100*1000:.1f}毫秒")
    
    # 批量指纹生成测试
    start_time = time.time()
    generator.generate_batch_fingerprints(30)
    batch_time = time.time() - start_time
    
    print(f"批量指纹生成 (30个): {batch_time:.3f}秒")
    print(f"平均时间: {batch_time/30*1000:.1f}毫秒")

def test_device_manager_performance():
    """测试设备管理器性能"""
    manager = DeviceManager()
    
    # 设备创建测试
    start_time = time.time()
    for i in range(30):
        manager.create_device(i)
    creation_time = time.time() - start_time
    
    print(f"设备创建 (30个): {creation_time:.3f}秒")
    
    # 设备查询测试
    start_time = time.time()
    for i in range(1000):
        manager.get_available_device()
    query_time = time.time() - start_time
    
    print(f"设备查询 (1000次): {query_time:.3f}秒")

if __name__ == "__main__":
    print("=== 性能测试报告 ===")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("1. 指纹生成性能测试")
    test_fingerprint_generation_performance()
    print()
    
    print("2. 设备管理器性能测试")
    test_device_manager_performance()
    print()
    
    print("=== 性能测试完成 ===")
EOF
    
    # 运行性能测试
    python3 "$REPORT_DIR/performance_test_$TIMESTAMP.py" > "$REPORT_DIR/performance_result_$TIMESTAMP.txt"
    
    log_info "性能测试完成，报告保存到: $REPORT_DIR/performance_result_$TIMESTAMP.txt"
}

# 运行集成测试
run_integration_test() {
    log_test "运行集成测试"
    
    # 检查服务是否运行
    if curl -s http://localhost:8888/health > /dev/null 2>&1; then
        log_info "检测到运行中的服务，执行集成测试"
        
        # 运行API测试脚本
        if [ -f "$PROJECT_ROOT/scripts/test_api.sh" ]; then
            bash "$PROJECT_ROOT/scripts/test_api.sh" > "$REPORT_DIR/integration_test_$TIMESTAMP.txt" 2>&1
            
            if [ $? -eq 0 ]; then
                log_info "集成测试通过"
            else
                log_warning "集成测试部分失败，请查看报告"
            fi
        else
            log_warning "未找到API测试脚本"
        fi
    else
        log_warning "服务未运行，跳过集成测试"
    fi
}

# 生成测试摘要报告
generate_summary_report() {
    log_test "生成测试摘要报告"
    
    summary_file="$REPORT_DIR/test_summary_$TIMESTAMP.md"
    
    cat > "$summary_file" << EOF
# 测试摘要报告

## 测试信息
- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **项目路径**: $PROJECT_ROOT
- **Python版本**: $(python3 --version 2>&1)

## 测试结果

### 单元测试
- F5指纹生成器测试: 查看 f5_generator_report_$TIMESTAMP.html
- API接口测试: 查看 api_test_report_$TIMESTAMP.html

### 代码覆盖率
- 覆盖率报告: 查看 coverage_report_$TIMESTAMP.txt
- HTML报告: 查看 coverage_html_$TIMESTAMP/index.html

### 性能测试
- 性能测试结果: 查看 performance_result_$TIMESTAMP.txt

### 集成测试
- 集成测试结果: 查看 integration_test_$TIMESTAMP.txt

## 文件列表
EOF
    
    # 添加生成的文件列表
    echo "### 生成的测试文件" >> "$summary_file"
    ls -la "$REPORT_DIR"/*"$TIMESTAMP"* | while read line; do
        echo "- $line" >> "$summary_file"
    done
    
    log_info "测试摘要报告生成: $summary_file"
}

# 清理旧报告
cleanup_old_reports() {
    log_test "清理旧测试报告"
    
    # 保留最近10次的测试报告
    if [ -d "$REPORT_DIR" ]; then
        find "$REPORT_DIR" -name "*.html" -mtime +7 -delete 2>/dev/null || true
        find "$REPORT_DIR" -name "*.txt" -mtime +7 -delete 2>/dev/null || true
        find "$REPORT_DIR" -type d -name "coverage_html_*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
        
        log_info "清理完成"
    fi
}

# 主函数
main() {
    log_info "开始运行测试套件"
    echo "项目路径: $PROJECT_ROOT"
    echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    # 创建报告目录
    create_report_dir
    
    # 检查环境
    check_python_env
    
    # 安装依赖
    install_test_dependencies
    
    # 清理旧报告
    cleanup_old_reports
    
    # 运行测试
    run_unit_tests
    echo ""
    
    run_coverage_test
    echo ""
    
    run_performance_test
    echo ""
    
    run_integration_test
    echo ""
    
    # 生成摘要
    generate_summary_report
    
    log_info "所有测试完成"
    log_info "测试报告保存在: $REPORT_DIR"
    
    # 显示摘要
    echo ""
    echo "=== 测试摘要 ==="
    echo "报告目录: $REPORT_DIR"
    echo "主要文件:"
    ls -la "$REPORT_DIR"/*"$TIMESTAMP"* 2>/dev/null | head -10
}

# 执行主函数
main "$@"
