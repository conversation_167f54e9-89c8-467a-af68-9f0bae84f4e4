#!/bin/bash

# 监控后台Web界面测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

echo "=========================================="
echo "监控后台Web界面测试"
echo "=========================================="

# 检测端口
MONITOR_PORT="9000"
NGINX_PORT="9094"

log_step "测试监控后台内部端口"
if curl -s "http://localhost:$MONITOR_PORT/health" > /dev/null; then
    log_info "内部端口 $MONITOR_PORT 响应正常"
else
    log_error "内部端口 $MONITOR_PORT 无响应，请检查服务是否启动"
    exit 1
fi

log_step "测试Web界面页面"

# 测试登录页面
if curl -s "http://localhost:$MONITOR_PORT/login" | grep -q "星巴克风控绕过系统"; then
    log_info "登录页面加载正常"
else
    log_warn "登录页面可能有问题"
fi

# 测试根路径重定向
if curl -s "http://localhost:$MONITOR_PORT/" | grep -q "Redirecting"; then
    log_info "根路径重定向正常"
else
    log_warn "根路径重定向可能有问题"
fi

# 测试API文档
if curl -s "http://localhost:$MONITOR_PORT/docs" | grep -q "FastAPI"; then
    log_info "API文档页面正常"
else
    log_warn "API文档页面可能有问题"
fi

log_step "测试Nginx代理端口"
if curl -s "http://localhost:$NGINX_PORT/health" > /dev/null 2>&1; then
    log_info "Nginx代理端口 $NGINX_PORT 响应正常"
    
    # 测试代理的Web界面
    if curl -s "http://localhost:$NGINX_PORT/login" | grep -q "星巴克风控绕过系统"; then
        log_info "Nginx代理的Web界面正常"
    else
        log_warn "Nginx代理的Web界面可能有问题"
    fi
else
    log_warn "Nginx代理端口 $NGINX_PORT 无响应"
    log_warn "可能没有配置Nginx代理，只能通过内部端口访问"
fi

log_step "测试静态文件"
if curl -s "http://localhost:$MONITOR_PORT/static/css/dashboard.css" | grep -q "dashboard"; then
    log_info "CSS静态文件加载正常"
else
    log_warn "CSS静态文件可能有问题"
fi

if curl -s "http://localhost:$MONITOR_PORT/static/js/dashboard.js" | grep -q "function"; then
    log_info "JavaScript静态文件加载正常"
else
    log_warn "JavaScript静态文件可能有问题"
fi

echo ""
echo "=========================================="
echo "Web界面测试完成"
echo "=========================================="
echo ""
echo "访问地址:"
echo "- 登录页面: http://localhost:$MONITOR_PORT/login"
echo "- 管理后台: http://localhost:$MONITOR_PORT/dashboard"
echo "- API文档: http://localhost:$MONITOR_PORT/docs"

if curl -s "http://localhost:$NGINX_PORT/health" > /dev/null 2>&1; then
    echo ""
    echo "Nginx代理访问:"
    echo "- 登录页面: http://服务器IP:$NGINX_PORT/login"
    echo "- 管理后台: http://服务器IP:$NGINX_PORT/dashboard"
    echo "- API文档: http://服务器IP:$NGINX_PORT/docs"
fi

echo ""
echo "登录信息:"
echo "- 管理员账户: monitor_admin"
echo "- 登录密码: MonitorAdmin2025#Backend!"
echo ""
log_info "Web界面测试完成！"
