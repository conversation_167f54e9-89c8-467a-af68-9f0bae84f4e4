#!/bin/bash

# 星巴克风控绕过系统 - 独立监控后台部署脚本
# 专用于监控所有客户接口使用情况，防止客户装后门捣乱

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

echo "=========================================="
echo "星巴克风控绕过系统 - 独立监控后台部署"
echo "=========================================="

# 检测当前用户和项目路径
CURRENT_USER=$(whoami)
if [[ "$CURRENT_USER" == "monitor" ]]; then
    # monitor用户运行，使用当前目录
    PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    MONITOR_USER="monitor"
    log_info "检测到monitor用户运行，项目路径: $PROJECT_DIR"
elif [[ "$EUID" -eq 0 ]]; then
    # root用户运行，使用monitor用户目录
    MONITOR_USER="monitor"
    PROJECT_DIR="/home/<USER>/monitor_backend"
    log_info "检测到root用户运行，将为monitor用户部署"
else
    log_error "请使用monitor用户或root用户运行此脚本"
    log_error "monitor用户: ./deploy_monitor.sh"
    log_error "root用户: sudo ./deploy_monitor.sh"
    exit 1
fi

# 配置变量
MONITOR_PORT="9000"
SERVICE_NAME="monitor-backend"

# 验证项目目录
log_step "验证项目环境"
if [[ ! -f "$PROJECT_DIR/src/monitor_app.py" ]]; then
    log_error "未找到监控后台核心文件: $PROJECT_DIR/src/monitor_app.py"
    exit 1
fi
log_info "项目环境验证通过"

log_step "创建项目目录结构"
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$PROJECT_DIR/data"

log_step "进入项目目录"
cd "$PROJECT_DIR"

log_step "检查Python环境"
if ! command -v python3 &> /dev/null; then
    log_error "Python3未安装，请先安装Python3"
    log_error "Ubuntu/Debian: sudo apt-get install python3 python3-pip python3-venv"
    exit 1
fi
log_info "Python3环境检查通过"

log_step "创建Python虚拟环境"
if [[ "$CURRENT_USER" == "monitor" ]]; then
    # monitor用户直接运行
    python3 -m venv venv
    ./venv/bin/pip install --upgrade pip
else
    # root用户为monitor用户创建
    sudo -u "$MONITOR_USER" python3 -m venv venv
    sudo -u "$MONITOR_USER" ./venv/bin/pip install --upgrade pip
fi

log_step "安装Python依赖包"
if [[ -f requirements.txt ]]; then
    if [[ "$CURRENT_USER" == "monitor" ]]; then
        ./venv/bin/pip install -r requirements.txt
    else
        sudo -u "$MONITOR_USER" ./venv/bin/pip install -r requirements.txt
    fi
else
    log_error "未找到requirements.txt文件"
    exit 1
fi

log_step "配置环境变量"
if [[ ! -f "$PROJECT_DIR/.env" ]]; then
    log_info "创建环境配置文件"
    cat > "$PROJECT_DIR/.env" << EOF
# 监控后台配置
HOST=0.0.0.0
PORT=$MONITOR_PORT
DEBUG=false

# 安全配置
SECRET_KEY=Monitor2025BackendSecretKey789
ACCESS_TOKEN_EXPIRE_MINUTES=60
ADMIN_USERNAME=monitor_admin
ADMIN_PASSWORD=MonitorAdmin2025#Backend!

# 数据库配置
DATABASE_URL=sqlite:///./data/monitor_logs.db

# 主系统集成配置
MAIN_SYSTEM_URL=http://localhost:8888
MAIN_SYSTEM_TOKEN=monitor_backend_secret_token_2025

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/monitor_backend.log

# 监控配置
MONITOR_INTERVAL=60
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=80
ALERT_THRESHOLD_DISK=90
EOF

    if [[ "$CURRENT_USER" == "root" ]]; then
        chown "$MONITOR_USER:$MONITOR_USER" "$PROJECT_DIR/.env"
    fi
    chmod 600 "$PROJECT_DIR/.env"
    log_info "环境配置文件创建完成"
else
    log_info "环境配置文件已存在，跳过创建"
fi

log_step "创建启动脚本"
cat > "$PROJECT_DIR/start_service.sh" << EOF
#!/bin/bash

# 监控后台启动脚本
# 用于systemd服务启动

set -e

# 切换到项目目录
cd $PROJECT_DIR

# 激活虚拟环境并启动服务
source ./venv/bin/activate

# 启动uvicorn服务
exec uvicorn src.monitor_app:app --host 0.0.0.0 --port 9000 --log-level info
EOF

chmod +x "$PROJECT_DIR/start_service.sh"
chown "$MONITOR_USER:$MONITOR_USER" "$PROJECT_DIR/start_service.sh"
log_info "启动脚本创建完成"

log_step "创建systemd服务"
if [[ "$CURRENT_USER" == "root" ]]; then
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=星巴克风控绕过系统 - 独立监控后台
After=network.target

[Service]
Type=exec
User=$MONITOR_USER
Group=$MONITOR_USER
WorkingDirectory=$PROJECT_DIR
ExecStart=$PROJECT_DIR/start_service.sh
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF
    log_info "systemd服务文件创建完成"
else
    log_info "非root用户，跳过systemd服务创建"
    log_info "如需创建服务，请使用root权限运行: sudo ./deploy_monitor.sh"
fi

log_step "配置Nginx反向代理"
if [[ "$CURRENT_USER" == "root" ]]; then
    cat > /etc/nginx/sites-available/monitor-backend << 'EOF'
server {
    listen 9094;
    server_name _;

    # 安全头设置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 主要代理配置
    location / {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
    }

    # API接口代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 静态文件代理
    location /static/ {
        proxy_pass http://127.0.0.1:9000/static/;
        proxy_set_header Host $host;
        proxy_cache_valid 200 1h;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }

    # API文档代理
    location /docs {
        proxy_pass http://127.0.0.1:9000/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # OpenAPI规范代理
    location /openapi.json {
        proxy_pass http://127.0.0.1:9000/openapi.json;
        proxy_set_header Host $host;
    }

    # 健康检查代理
    location /health {
        proxy_pass http://127.0.0.1:9000/health;
        proxy_set_header Host $host;
        access_log off;
    }
}
EOF

    # 启用站点
    ln -sf /etc/nginx/sites-available/monitor-backend /etc/nginx/sites-enabled/

    # 测试Nginx配置
    if nginx -t; then
        systemctl reload nginx
        log_info "Nginx配置完成并重载成功"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
else
    log_info "非root用户，跳过Nginx配置"
    log_info "如需配置Nginx，请使用root权限运行: sudo ./deploy_monitor.sh"
    log_warn "注意：没有Nginx代理，Web界面只能通过内部端口访问"
    log_warn "内部访问地址: http://localhost:$MONITOR_PORT"
fi

log_step "配置防火墙"
if [[ "$CURRENT_USER" == "root" ]]; then
    if command -v ufw &> /dev/null; then
        ufw allow 9094/tcp comment "Monitor Backend Web Interface"
        log_info "防火墙规则已添加: 端口 9094 (Nginx代理)"
    fi
else
    log_info "非root用户，跳过防火墙配置"
fi

log_step "启动监控后台服务"
if [[ "$CURRENT_USER" == "root" ]]; then
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    systemctl start "$SERVICE_NAME"

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 8

    log_step "验证部署"
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "监控后台服务启动成功"

        # 测试API
        if curl -s "http://localhost:$MONITOR_PORT/health" > /dev/null; then
            log_info "监控后台API响应正常"
        else
            log_warn "监控后台API响应异常，请检查日志"
        fi
    else
        log_error "监控后台服务启动失败"
        echo ""
        echo "服务状态:"
        systemctl status "$SERVICE_NAME" --no-pager -l
        echo ""
        echo "最近的服务日志:"
        journalctl -u "$SERVICE_NAME" --no-pager -n 20
        echo ""
        log_error "请检查上述日志信息排查问题"
        exit 1
    fi
else
    log_info "非root用户，手动启动监控后台"
    log_info "启动命令: cd $PROJECT_DIR && ./start_service.sh"

    log_step "测试启动"
    cd "$PROJECT_DIR"
    timeout 10s ./start_service.sh &
    sleep 3

    if curl -s "http://localhost:$MONITOR_PORT/health" > /dev/null; then
        log_info "监控后台测试启动成功"
        pkill -f "uvicorn.*monitor_app" 2>/dev/null || true
    else
        log_warn "监控后台测试启动失败，请检查配置"
        pkill -f "uvicorn.*monitor_app" 2>/dev/null || true
    fi
fi

log_step "部署完成"
echo ""
echo "=========================================="
echo "监控后台部署完成!"
echo "=========================================="
echo ""
echo "部署信息:"
if [[ "$CURRENT_USER" == "monitor" ]]; then
    echo "- 运行用户: monitor (当前用户)"
    echo "- 项目目录: $PROJECT_DIR"
    echo "- 启动方式: 手动启动"
    echo ""
    echo "启动命令:"
    echo "  cd $PROJECT_DIR"
    echo "  ./start_service.sh"
    echo ""
    echo "Web界面访问:"
    echo "- 内部访问: http://localhost:$MONITOR_PORT"
    echo "- 登录页面: http://localhost:$MONITOR_PORT/login"
    echo "- 管理后台: http://localhost:$MONITOR_PORT/dashboard"
    echo ""
    echo "注意: 没有Nginx代理，只能通过内部端口访问"
    echo "如需外部访问，请使用root权限重新运行部署脚本"
else
    echo "- 运行用户: $MONITOR_USER"
    echo "- 项目目录: $PROJECT_DIR"
    echo "- 服务名称: $SERVICE_NAME"
    echo "- 启动方式: systemd服务"
    echo ""
    echo "服务管理:"
    echo "  sudo systemctl start $SERVICE_NAME"
    echo "  sudo systemctl stop $SERVICE_NAME"
    echo "  sudo systemctl status $SERVICE_NAME"
    echo ""
    echo "Web界面访问:"
    echo "- 外部访问: http://服务器IP:9094"
    echo "- 内部访问: http://localhost:$MONITOR_PORT"
    echo "- 登录页面: http://服务器IP:9094/login"
    echo "- 管理后台: http://服务器IP:9094/dashboard"
    echo "- API文档: http://服务器IP:9094/docs"
fi
echo ""
echo "登录信息:"
echo "- 管理员账户: admin"
echo "- 登录密码: admin123456"
echo ""
echo "Web界面功能:"
echo "1. 系统概览 - 实时监控数据统计"
echo "2. 客户管理 - 查看所有客户API使用情况"
echo "3. 接口监控 - 详细的API调用记录"
echo "4. 异常告警 - 可疑行为检测和告警"
echo "5. 数据分析 - 客户行为模式分析"
echo "6. 系统设置 - 监控参数配置"
echo ""
echo "配置文件:"
echo "- 环境配置: $PROJECT_DIR/.env"
echo "- 数据库: $PROJECT_DIR/data/monitor_logs.db"
echo "- 日志文件: $PROJECT_DIR/logs/monitor_backend.log"
echo ""
echo "注意事项:"
echo "1. 监控后台独立运行，与主系统分离"
echo "2. 自动接收主系统发送的客户API使用数据"
echo "3. 提供完整的Web管理界面"
echo "4. 支持实时监控和历史数据查询"
echo "5. 具备客户行为分析和风险评估功能"
echo ""
log_info "监控后台部署完成！请访问Web界面查看监控数据"
