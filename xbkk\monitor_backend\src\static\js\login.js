// 星巴克风控绕过系统 - 监控后台登录JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经登录
    checkExistingLogin();
    
    // 绑定表单提交事件
    bindLoginForm();
    
    // 绑定键盘事件
    bindKeyboardEvents();
});

// 检查现有登录状态
function checkExistingLogin() {
    const token = localStorage.getItem('access_token');
    const rememberLogin = localStorage.getItem('remember_login');
    
    if (token && rememberLogin === 'true') {
        // 验证token是否仍然有效
        fetch('/api/auth/verify', {
            headers: {
                'Authorization': 'Bearer ' + token
            }
        }).then(response => {
            if (response.ok) {
                // Token有效，直接跳转到后台
                window.location.href = '/dashboard';
            } else {
                // Token无效，清除本地存储
                clearLocalStorage();
            }
        }).catch(error => {
            console.error('验证登录状态失败:', error);
            clearLocalStorage();
        });
    }
}

// 绑定登录表单事件
function bindLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
}

// 绑定键盘事件
function bindKeyboardEvents() {
    // 回车键提交表单
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                e.preventDefault();
                handleLogin(e);
            }
        }
    });
    
    // ESC键清除错误提示
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideLoginError();
        }
    });
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);
    
    // 获取表单数据
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password'),
        remember_me: formData.get('rememberMe') === 'on'
    };
    
    // 验证输入
    if (!validateLoginInput(loginData)) {
        return;
    }
    
    // 显示加载状态
    setLoadingState(submitButton, true);
    hideLoginError();
    
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            // 登录成功
            handleLoginSuccess(result, loginData.remember_me);
        } else {
            // 登录失败
            handleLoginError(result.message || '登录失败，请检查账户和密码');
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        handleLoginError('网络连接失败，请稍后重试');
    } finally {
        // 恢复按钮状态
        setLoadingState(submitButton, false);
    }
}

// 验证登录输入
function validateLoginInput(loginData) {
    if (!loginData.username || loginData.username.trim() === '') {
        showLoginError('请输入管理员账户');
        focusField('username');
        return false;
    }
    
    if (!loginData.password || loginData.password.trim() === '') {
        showLoginError('请输入登录密码');
        focusField('password');
        return false;
    }
    
    if (loginData.username.length < 3) {
        showLoginError('账户名称至少需要3个字符');
        focusField('username');
        return false;
    }
    
    if (loginData.password.length < 6) {
        showLoginError('密码至少需要6个字符');
        focusField('password');
        return false;
    }
    
    return true;
}

// 处理登录成功
function handleLoginSuccess(result, rememberMe) {
    // 保存访问令牌
    localStorage.setItem('access_token', result.access_token);
    
    // 保存记住登录状态
    if (rememberMe) {
        localStorage.setItem('remember_login', 'true');
    } else {
        localStorage.removeItem('remember_login');
    }
    
    // 显示成功消息
    showLoginSuccess('登录成功，正在跳转...');
    
    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
        window.location.href = '/dashboard';
    }, 1000);
}

// 处理登录错误
function handleLoginError(message) {
    showLoginError(message);
    
    // 清空密码字段
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.value = '';
        passwordField.focus();
    }
}

// 显示登录错误
function showLoginError(message) {
    const alertDiv = document.getElementById('loginAlert');
    const messageSpan = document.getElementById('loginAlertMessage');
    
    if (alertDiv && messageSpan) {
        messageSpan.textContent = message;
        alertDiv.className = 'alert alert-danger login-alert';
        alertDiv.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(hideLoginError, 3000);
    }
}

// 显示登录成功
function showLoginSuccess(message) {
    const alertDiv = document.getElementById('loginAlert');
    const messageSpan = document.getElementById('loginAlertMessage');
    
    if (alertDiv && messageSpan) {
        messageSpan.textContent = message;
        alertDiv.className = 'alert alert-success login-alert';
        alertDiv.style.display = 'block';
    }
}

// 隐藏登录提示
function hideLoginError() {
    const alertDiv = document.getElementById('loginAlert');
    if (alertDiv) {
        alertDiv.style.display = 'none';
    }
}

// 设置加载状态
function setLoadingState(button, loading) {
    if (!button) return;
    
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
        button.innerHTML = '<span class="loading"></span> 登录中...';
    } else {
        button.classList.remove('loading');
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> 登录系统';
    }
}

// 聚焦字段
function focusField(fieldId) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.focus();
        field.select();
    }
}

// 清除本地存储
function clearLocalStorage() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('remember_login');
}

// 密码显示/隐藏切换
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput && toggleIcon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }
}

// 表单字段验证样式
function validateField(fieldId, isValid) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.classList.remove('is-valid', 'is-invalid');
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');
    }
}

// 实时验证用户名
function validateUsername() {
    const username = document.getElementById('username').value;
    const isValid = username.length >= 3;
    validateField('username', isValid);
    return isValid;
}

// 实时验证密码
function validatePassword() {
    const password = document.getElementById('password').value;
    const isValid = password.length >= 6;
    validateField('password', isValid);
    return isValid;
}

// 绑定实时验证事件
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    
    if (usernameField) {
        usernameField.addEventListener('blur', validateUsername);
        usernameField.addEventListener('input', function() {
            if (this.value.length > 0) {
                validateUsername();
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }
    
    if (passwordField) {
        passwordField.addEventListener('blur', validatePassword);
        passwordField.addEventListener('input', function() {
            if (this.value.length > 0) {
                validatePassword();
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }
});

// 页面可见性变化时检查登录状态
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        checkExistingLogin();
    }
});

// 防止多次提交
let isSubmitting = false;

function preventMultipleSubmit(callback) {
    return function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        
        isSubmitting = true;
        
        // 执行回调
        const result = callback.call(this, e);
        
        // 重置提交状态
        setTimeout(() => {
            isSubmitting = false;
        }, 1000);
        
        return result;
    };
}

// 应用防止多次提交
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        const originalHandler = handleLogin;
        loginForm.removeEventListener('submit', originalHandler);
        loginForm.addEventListener('submit', preventMultipleSubmit(originalHandler));
    }
});
