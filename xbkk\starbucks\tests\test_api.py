#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口测试
作者：YINGAshadow
创建时间：2025-7-29
功能：测试API接口功能
"""

import unittest
import asyncio
import sys
import os
import json
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from fastapi.testclient import TestClient
from src.api.main import app


class TestAPIEndpoints(unittest.TestCase):
    """API接口测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """测试根路径"""
        response = self.client.get("/")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("version", data["data"])
    
    def test_health_check(self):
        """测试健康检查"""
        response = self.client.get("/health")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
    
    def test_generate_fingerprint(self):
        """测试生成指纹"""
        payload = {"device_count": 3}
        response = self.client.post("/api/v1/fingerprint/generate", json=payload)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(len(data["fingerprints"]), 3)
        
        # 检查指纹格式
        fingerprint = data["fingerprints"][0]
        self.assertIn("x-device-id", fingerprint)
        self.assertIn("Authorization", fingerprint)
    
    def test_generate_fingerprint_invalid_count(self):
        """测试无效设备数量"""
        # 测试负数
        payload = {"device_count": -1}
        response = self.client.post("/api/v1/fingerprint/generate", json=payload)
        self.assertEqual(response.status_code, 400)
        
        # 测试超过最大值
        payload = {"device_count": 100}
        response = self.client.post("/api/v1/fingerprint/generate", json=payload)
        self.assertEqual(response.status_code, 400)
    
    def test_get_device_fingerprint(self):
        """测试获取设备指纹"""
        # 先生成指纹
        payload = {"device_count": 1}
        self.client.post("/api/v1/fingerprint/generate", json=payload)
        
        # 获取指纹
        response = self.client.get("/api/v1/fingerprint/0")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(len(data["fingerprints"]), 1)
    
    def test_get_device_fingerprint_invalid_index(self):
        """测试无效设备索引"""
        response = self.client.get("/api/v1/fingerprint/999")
        self.assertEqual(response.status_code, 400)
    
    def test_get_devices(self):
        """测试获取设备列表"""
        response = self.client.get("/api/v1/devices")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("devices", data["data"])
    
    def test_device_operation(self):
        """测试设备操作"""
        # 先生成设备
        payload = {"device_count": 2}
        self.client.post("/api/v1/fingerprint/generate", json=payload)
        
        # 测试重新生成
        operation_payload = {
            "device_index": 0,
            "operation": "regenerate"
        }
        response = self.client.post("/api/v1/devices/operation", json=operation_payload)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
    
    def test_device_operation_invalid(self):
        """测试无效设备操作"""
        payload = {
            "device_index": 999,
            "operation": "regenerate"
        }
        response = self.client.post("/api/v1/devices/operation", json=payload)
        self.assertEqual(response.status_code, 400)
    
    def test_cleanup_devices(self):
        """测试清理设备"""
        response = self.client.post("/api/v1/devices/cleanup")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
    
    @patch('src.utils.bypass_tester.BypassTester.test_single_device')
    def test_bypass_single_device(self, mock_test):
        """测试单设备风控测试"""
        # 模拟测试结果
        mock_test.return_value = {
            "device_index": 0,
            "success": True,
            "response_time": 0.5,
            "status_code": 200
        }
        
        # 先生成设备
        payload = {"device_count": 1}
        self.client.post("/api/v1/fingerprint/generate", json=payload)
        
        # 测试风控
        test_payload = {
            "device_index": 0,
            "concurrent_count": 1
        }
        response = self.client.post("/api/v1/test/bypass", json=test_payload)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("test_results", data)
    
    @patch('src.utils.bypass_tester.BypassTester.test_concurrent_devices')
    def test_bypass_concurrent_devices(self, mock_test):
        """测试并发设备风控测试"""
        # 模拟测试结果
        mock_test.return_value = [
            {"device_index": 0, "success": True, "response_time": 0.5},
            {"device_index": 1, "success": True, "response_time": 0.6},
            {"device_index": 2, "success": False, "response_time": 1.0}
        ]
        
        # 先生成设备
        payload = {"device_count": 3}
        self.client.post("/api/v1/fingerprint/generate", json=payload)
        
        # 测试并发风控
        test_payload = {"concurrent_count": 3}
        response = self.client.post("/api/v1/test/bypass", json=test_payload)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(len(data["test_results"]), 3)
        self.assertIn("success_rate", data["data"])


class TestAPIPerformance(unittest.TestCase):
    """API性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.client = TestClient(app)
    
    def test_response_time(self):
        """测试响应时间"""
        import time
        
        # 测试健康检查响应时间
        start_time = time.time()
        response = self.client.get("/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        self.assertEqual(response.status_code, 200)
        self.assertLess(response_time, 1.0, "健康检查响应时间应小于1秒")
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        import threading
        import time
        
        results = []
        
        def make_request():
            start_time = time.time()
            response = self.client.get("/health")
            end_time = time.time()
            results.append({
                "status_code": response.status_code,
                "response_time": end_time - start_time
            })
        
        # 创建10个并发请求
        threads = []
        for i in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # 启动所有线程
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 检查结果
        self.assertEqual(len(results), 10)
        for result in results:
            self.assertEqual(result["status_code"], 200)
            self.assertLess(result["response_time"], 2.0)
        
        # 总时间应该合理
        self.assertLess(total_time, 5.0, "10个并发请求总时间应小于5秒")
    
    def test_large_batch_generation(self):
        """测试大批量指纹生成"""
        import time
        
        payload = {"device_count": 30}
        
        start_time = time.time()
        response = self.client.post("/api/v1/fingerprint/generate", json=payload)
        end_time = time.time()
        
        generation_time = end_time - start_time
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(len(data["fingerprints"]), 30)
        
        # 30个指纹生成时间应该合理
        self.assertLess(generation_time, 5.0, "30个指纹生成时间应小于5秒")


class TestAPIErrorHandling(unittest.TestCase):
    """API错误处理测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.client = TestClient(app)
    
    def test_invalid_json(self):
        """测试无效JSON"""
        response = self.client.post(
            "/api/v1/fingerprint/generate",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        self.assertEqual(response.status_code, 422)
    
    def test_missing_fields(self):
        """测试缺少字段"""
        response = self.client.post("/api/v1/fingerprint/generate", json={})
        # 应该使用默认值，不报错
        self.assertEqual(response.status_code, 200)
    
    def test_invalid_device_index(self):
        """测试无效设备索引"""
        response = self.client.get("/api/v1/fingerprint/-1")
        self.assertEqual(response.status_code, 400)
        
        response = self.client.get("/api/v1/fingerprint/abc")
        self.assertEqual(response.status_code, 422)
    
    def test_invalid_operation(self):
        """测试无效操作"""
        payload = {
            "device_index": 0,
            "operation": "invalid_operation"
        }
        response = self.client.post("/api/v1/devices/operation", json=payload)
        self.assertEqual(response.status_code, 400)
    
    def test_nonexistent_endpoints(self):
        """测试不存在的端点"""
        response = self.client.get("/api/v1/nonexistent")
        self.assertEqual(response.status_code, 404)
        
        response = self.client.post("/api/v1/nonexistent")
        self.assertEqual(response.status_code, 404)


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加功能测试
    suite.addTest(unittest.makeSuite(TestAPIEndpoints))
    
    # 添加性能测试
    suite.addTest(unittest.makeSuite(TestAPIPerformance))
    
    # 添加错误处理测试
    suite.addTest(unittest.makeSuite(TestAPIErrorHandling))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    print(f"\n测试结果:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # 退出码
    sys.exit(0 if result.wasSuccessful() else 1)
