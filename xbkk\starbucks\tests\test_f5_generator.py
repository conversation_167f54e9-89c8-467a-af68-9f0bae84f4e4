#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
F5 Shape指纹生成器测试
作者：YINGAshadow
创建时间：2025-7-29
功能：测试F5 Shape指纹生成功能
"""

import unittest
import sys
import os
import base64
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.f5_shape_generator import F5ShapeGenerator


class TestF5ShapeGenerator(unittest.TestCase):
    """F5 Shape指纹生成器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = F5ShapeGenerator()
    
    def test_generator_initialization(self):
        """测试生成器初始化"""
        self.assertIsNotNone(self.generator)
        self.assertIsNotNone(self.generator.base_fingerprint_data)
        self.assertIn("x-device-id", self.generator.base_fingerprint_data)
    
    def test_generate_single_fingerprint(self):
        """测试生成单个指纹"""
        fingerprint = self.generator.generate_fingerprint(0)
        
        # 检查必要字段存在
        required_fields = [
            "x-device-id",
            "X-XHPAcPXq-z",
            "X-XHPAcPXq-g",
            "X-XHPAcPXq-e",
            "X-XHPAcPXq-a",
            "Authorization",
            "time"
        ]
        
        for field in required_fields:
            self.assertIn(field, fingerprint, f"缺少必要字段: {field}")
        
        # 检查字段格式
        self.assertEqual(fingerprint["X-XHPAcPXq-z"], "q")
        self.assertTrue(len(fingerprint["x-device-id"]) > 0)
        self.assertTrue(len(fingerprint["Authorization"]) > 0)
    
    def test_generate_batch_fingerprints(self):
        """测试批量生成指纹"""
        count = 5
        fingerprints = self.generator.generate_batch_fingerprints(count)
        
        self.assertEqual(len(fingerprints), count)
        
        # 检查每个指纹的唯一性
        device_ids = [fp["x-device-id"] for fp in fingerprints]
        self.assertEqual(len(device_ids), len(set(device_ids)), "设备ID应该唯一")
    
    def test_base64_encoding(self):
        """测试Base64编码字段"""
        fingerprint = self.generator.generate_fingerprint(0)
        
        # 检查Base64字段可以正确解码
        base64_fields = ["X-XHPAcPXq-g", "X-XHPAcPXq-e"]
        
        for field in base64_fields:
            if field in fingerprint:
                try:
                    decoded = base64.b64decode(fingerprint[field])
                    self.assertIsNotNone(decoded)
                except Exception as e:
                    self.fail(f"字段 {field} 不是有效的Base64编码: {e}")
    
    def test_device_variants(self):
        """测试设备变体生成"""
        fingerprint1 = self.generator.generate_fingerprint(0)
        fingerprint2 = self.generator.generate_fingerprint(1)
        
        # 不同设备应该有不同的指纹
        self.assertNotEqual(
            fingerprint1["x-device-id"], 
            fingerprint2["x-device-id"]
        )
        
        # 固定字段应该相同
        self.assertEqual(
            fingerprint1["X-XHPAcPXq-z"], 
            fingerprint2["X-XHPAcPXq-z"]
        )
    
    def test_time_format(self):
        """测试时间格式"""
        fingerprint = self.generator.generate_fingerprint(0)
        time_str = fingerprint.get("time", "")
        
        # 检查时间格式 YYYY-MM-DD HH:MM:SS
        import re
        time_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
        self.assertTrue(
            re.match(time_pattern, time_str),
            f"时间格式不正确: {time_str}"
        )
    
    def test_authorization_format(self):
        """测试Authorization格式"""
        fingerprint = self.generator.generate_fingerprint(0)
        auth = fingerprint.get("Authorization", "")
        
        # Authorization应该是32位MD5哈希
        self.assertEqual(len(auth), 32)
        self.assertTrue(all(c in '0123456789abcdef' for c in auth.lower()))
    
    def test_consistency(self):
        """测试一致性"""
        # 同一设备索引应该生成相同的基础结构
        fp1 = self.generator.generate_fingerprint(0)
        fp2 = self.generator.generate_fingerprint(0)
        
        # 固定字段应该相同
        fixed_fields = ["X-XHPAcPXq-z", "X-XHPAcPXq-f"]
        for field in fixed_fields:
            if field in fp1 and field in fp2:
                self.assertEqual(fp1[field], fp2[field])
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试负数设备索引
        try:
            fingerprint = self.generator.generate_fingerprint(-1)
            self.assertIsNotNone(fingerprint)
        except Exception:
            pass  # 允许抛出异常
        
        # 测试大设备索引
        try:
            fingerprint = self.generator.generate_fingerprint(1000)
            self.assertIsNotNone(fingerprint)
        except Exception:
            pass  # 允许抛出异常


class TestF5ShapeGeneratorPerformance(unittest.TestCase):
    """F5 Shape指纹生成器性能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = F5ShapeGenerator()
    
    def test_single_generation_performance(self):
        """测试单个指纹生成性能"""
        import time
        
        start_time = time.time()
        fingerprint = self.generator.generate_fingerprint(0)
        end_time = time.time()
        
        generation_time = end_time - start_time
        self.assertLess(generation_time, 0.1, "单个指纹生成时间应小于100ms")
    
    def test_batch_generation_performance(self):
        """测试批量指纹生成性能"""
        import time
        
        count = 30
        start_time = time.time()
        fingerprints = self.generator.generate_batch_fingerprints(count)
        end_time = time.time()
        
        generation_time = end_time - start_time
        avg_time = generation_time / count
        
        self.assertEqual(len(fingerprints), count)
        self.assertLess(avg_time, 0.05, f"平均指纹生成时间应小于50ms，实际: {avg_time:.3f}s")
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 生成大量指纹
        for i in range(100):
            self.generator.generate_fingerprint(i)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该合理（小于50MB）
        self.assertLess(memory_increase, 50 * 1024 * 1024, "内存使用增长过多")


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加功能测试
    suite.addTest(unittest.makeSuite(TestF5ShapeGenerator))
    
    # 添加性能测试
    suite.addTest(unittest.makeSuite(TestF5ShapeGeneratorPerformance))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    print(f"\n测试结果:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 退出码
    sys.exit(0 if result.wasSuccessful() else 1)
