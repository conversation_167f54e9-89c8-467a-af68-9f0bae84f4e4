# 星巴克风控绕过系统 - 独立监控后台依赖包

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 异步支持
aiofiles==23.2.1
aiohttp==3.9.1

# 数据处理
pydantic==2.5.0
pydantic-settings==2.1.0

# 加密和安全
cryptography>=41.0.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
PyJWT==2.8.0

# 模板引擎
jinja2==3.1.2

# 数据库
# sqlite3 是Python内置模块，无需安装

# 日志和监控
structlog==23.2.0
psutil==5.9.6

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 时间处理
python-dateutil==2.8.2

# 配置管理
python-dotenv==1.0.0

# 数据验证
email-validator==2.1.0

# JSON处理
orjson==3.9.10

# 系统工具
click==8.1.7
rich==13.7.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
