#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备管理器
作者：YINGAshadow
创建时间：2025-7-29
功能：管理虚拟设备池和设备状态
"""

import asyncio
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .f5_shape_generator import f5_generator
from ..config.settings import settings


class DeviceStatus(Enum):
    """设备状态枚举"""
    AVAILABLE = "可用"
    BUSY = "忙碌"
    BLOCKED = "被封"
    MAINTENANCE = "维护中"


@dataclass
class Device:
    """设备数据类"""
    device_id: str
    device_index: int
    fingerprint: Dict[str, str]
    status: DeviceStatus
    last_used: datetime
    success_count: int
    failure_count: int
    created_at: datetime
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.failure_count
        if total == 0:
            return 1.0
        return self.success_count / total
    
    @property
    def is_healthy(self) -> bool:
        """设备是否健康"""
        return (self.status == DeviceStatus.AVAILABLE and 
                self.success_rate >= settings.SUCCESS_RATE_THRESHOLD)


class DeviceManager:
    """设备管理器类"""
    
    def __init__(self):
        """初始化设备管理器"""
        self.devices: Dict[int, Device] = {}
        self.db_path = settings.get_database_path()
        self._init_database()
        self._load_devices()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建设备表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS devices (
                    device_index INTEGER PRIMARY KEY,
                    device_id TEXT NOT NULL,
                    fingerprint TEXT NOT NULL,
                    status TEXT NOT NULL,
                    last_used TEXT,
                    success_count INTEGER DEFAULT 0,
                    failure_count INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL
                )
            """)
            
            # 创建使用记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS device_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_index INTEGER,
                    endpoint TEXT,
                    success BOOLEAN,
                    response_time REAL,
                    error_message TEXT,
                    timestamp TEXT,
                    FOREIGN KEY (device_index) REFERENCES devices (device_index)
                )
            """)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            raise Exception(f"初始化数据库失败: {str(e)}")
    
    def _load_devices(self):
        """从数据库加载设备"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM devices")
            rows = cursor.fetchall()
            
            for row in rows:
                device_index, device_id, fingerprint_json, status, last_used, success_count, failure_count, created_at = row
                
                device = Device(
                    device_id=device_id,
                    device_index=device_index,
                    fingerprint=json.loads(fingerprint_json),
                    status=DeviceStatus(status),
                    last_used=datetime.fromisoformat(last_used) if last_used else datetime.now(),
                    success_count=success_count,
                    failure_count=failure_count,
                    created_at=datetime.fromisoformat(created_at)
                )
                
                self.devices[device_index] = device
            
            conn.close()
            
            # 如果没有设备，创建初始设备池
            if not self.devices:
                self._create_initial_devices()
                
        except Exception as e:
            print(f"加载设备失败: {str(e)}")
            self._create_initial_devices()
    
    def _create_initial_devices(self):
        """创建初始设备池"""
        print("正在创建初始设备池...")
        
        for i in range(settings.MAX_DEVICES):
            try:
                fingerprint = f5_generator.generate_fingerprint(i)
                device = Device(
                    device_id=fingerprint["x-device-id"],
                    device_index=i,
                    fingerprint=fingerprint,
                    status=DeviceStatus.AVAILABLE,
                    last_used=datetime.now(),
                    success_count=0,
                    failure_count=0,
                    created_at=datetime.now()
                )
                
                self.devices[i] = device
                self._save_device(device)
                
            except Exception as e:
                print(f"创建设备 {i} 失败: {str(e)}")
        
        print(f"成功创建 {len(self.devices)} 个设备")
    
    def _save_device(self, device: Device):
        """保存设备到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO devices 
                (device_index, device_id, fingerprint, status, last_used, success_count, failure_count, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                device.device_index,
                device.device_id,
                json.dumps(device.fingerprint, ensure_ascii=False),
                device.status.value,
                device.last_used.isoformat(),
                device.success_count,
                device.failure_count,
                device.created_at.isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"保存设备失败: {str(e)}")
    
    def get_available_device(self) -> Optional[Device]:
        """获取可用设备"""
        available_devices = [
            device for device in self.devices.values()
            if device.status == DeviceStatus.AVAILABLE and device.is_healthy
        ]
        
        if not available_devices:
            return None
        
        # 选择最久未使用的设备
        device = min(available_devices, key=lambda d: d.last_used)
        device.status = DeviceStatus.BUSY
        device.last_used = datetime.now()
        self._save_device(device)
        
        return device
    
    def release_device(self, device_index: int, success: bool = True):
        """释放设备"""
        if device_index not in self.devices:
            return
        
        device = self.devices[device_index]
        device.status = DeviceStatus.AVAILABLE
        
        if success:
            device.success_count += 1
        else:
            device.failure_count += 1
            
            # 如果失败率过高，标记为被封
            if device.success_rate < settings.SUCCESS_RATE_THRESHOLD and device.failure_count > 5:
                device.status = DeviceStatus.BLOCKED
        
        self._save_device(device)
    
    def block_device(self, device_index: int, reason: str = ""):
        """封禁设备"""
        if device_index not in self.devices:
            return
        
        device = self.devices[device_index]
        device.status = DeviceStatus.BLOCKED
        self._save_device(device)
        
        print(f"设备 {device_index} 已被封禁: {reason}")
    
    def regenerate_device(self, device_index: int) -> bool:
        """重新生成设备指纹"""
        try:
            new_fingerprint = f5_generator.generate_fingerprint(device_index)
            
            if device_index in self.devices:
                device = self.devices[device_index]
                device.fingerprint = new_fingerprint
                device.device_id = new_fingerprint["x-device-id"]
                device.status = DeviceStatus.AVAILABLE
                device.success_count = 0
                device.failure_count = 0
                device.created_at = datetime.now()
            else:
                device = Device(
                    device_id=new_fingerprint["x-device-id"],
                    device_index=device_index,
                    fingerprint=new_fingerprint,
                    status=DeviceStatus.AVAILABLE,
                    last_used=datetime.now(),
                    success_count=0,
                    failure_count=0,
                    created_at=datetime.now()
                )
                self.devices[device_index] = device
            
            self._save_device(device)
            print(f"设备 {device_index} 指纹已重新生成")
            return True
            
        except Exception as e:
            print(f"重新生成设备 {device_index} 失败: {str(e)}")
            return False
    
    def get_device_stats(self) -> Dict:
        """获取设备统计信息"""
        stats = {
            "总设备数": len(self.devices),
            "可用设备": 0,
            "忙碌设备": 0,
            "被封设备": 0,
            "维护设备": 0,
            "健康设备": 0,
            "平均成功率": 0.0
        }
        
        total_success_rate = 0.0
        
        for device in self.devices.values():
            if device.status == DeviceStatus.AVAILABLE:
                stats["可用设备"] += 1
            elif device.status == DeviceStatus.BUSY:
                stats["忙碌设备"] += 1
            elif device.status == DeviceStatus.BLOCKED:
                stats["被封设备"] += 1
            elif device.status == DeviceStatus.MAINTENANCE:
                stats["维护设备"] += 1
            
            if device.is_healthy:
                stats["健康设备"] += 1
            
            total_success_rate += device.success_rate
        
        if self.devices:
            stats["平均成功率"] = total_success_rate / len(self.devices)
        
        return stats
    
    def get_device_list(self) -> List[Dict]:
        """获取设备列表"""
        device_list = []
        
        for device in self.devices.values():
            device_info = {
                "设备索引": device.device_index,
                "设备ID": device.device_id,
                "状态": device.status.value,
                "成功次数": device.success_count,
                "失败次数": device.failure_count,
                "成功率": f"{device.success_rate:.2%}",
                "最后使用": device.last_used.strftime("%Y-%m-%d %H:%M:%S"),
                "创建时间": device.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "是否健康": "是" if device.is_healthy else "否"
            }
            device_list.append(device_info)
        
        return device_list
    
    def cleanup_blocked_devices(self):
        """清理被封设备"""
        blocked_devices = [
            device for device in self.devices.values()
            if device.status == DeviceStatus.BLOCKED
        ]
        
        for device in blocked_devices:
            print(f"正在重新生成被封设备 {device.device_index}")
            self.regenerate_device(device.device_index)
    
    async def health_check(self):
        """健康检查"""
        print("开始设备健康检查...")
        
        unhealthy_count = 0
        for device in self.devices.values():
            if not device.is_healthy:
                unhealthy_count += 1
        
        if unhealthy_count > len(self.devices) * 0.3:  # 超过30%设备不健康
            print(f"警告: {unhealthy_count} 个设备不健康，建议进行维护")
            self.cleanup_blocked_devices()
        
        print("设备健康检查完成")


# 全局设备管理器实例
device_manager = DeviceManager()
