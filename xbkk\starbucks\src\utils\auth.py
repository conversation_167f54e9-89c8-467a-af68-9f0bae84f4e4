#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API认证模块
作者：YINGAshadow
创建时间：2025-7-29
功能：提供API访问认证和授权
"""

import hashlib
import os
import time
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict

from fastapi import HTTPException, Security, Depends, Header
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext

from ..config.settings import settings
from .logger import setup_logger


# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer认证
security = HTTPBearer()

# 日志记录器
logger = setup_logger(__name__)


class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        """初始化认证管理器"""
        self.secret_key = settings.SECRET_KEY
        self.algorithm = "HS256"
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        
        # 管理员密钥（生产环境从环境变量读取）
        admin_password = os.getenv("ADMIN_PASSWORD", "change_this_password_in_production")
        self.api_keys = {
            "admin": self.hash_password(admin_password)
        }

        # 客户API密钥（生产环境从数据库或配置文件读取）
        self.customer_api_keys = self._load_customer_api_keys()

    def _load_customer_api_keys(self) -> Dict[str, str]:
        """
        加载客户API密钥
        生产环境应从数据库或安全配置文件读取
        """
        # 从环境变量读取客户API密钥
        api_keys = {}

        # 支持多个客户密钥
        for i in range(1, 11):  # 支持最多10个客户
            key_name = f"CUSTOMER_API_KEY_{i}"
            customer_id = f"CUSTOMER_ID_{i}"

            api_key = os.getenv(key_name)
            cust_id = os.getenv(customer_id, f"customer_{i:03d}")

            if api_key:
                api_keys[cust_id] = api_key

        # 如果没有配置环境变量，使用默认密钥（仅用于演示）
        if not api_keys:
            logger.warning("未配置客户API密钥环境变量，使用默认密钥")
            api_keys = {
                "demo_customer": os.getenv("DEFAULT_API_KEY", "demo_api_key_change_in_production")
            }

        return api_keys
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        return encoded_jwt
    
    def verify_token(self, token: str) -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username: str = payload.get("sub")
            
            if username is None:
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证凭据",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return payload
            
        except JWTError:
            raise HTTPException(
                status_code=401,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def authenticate_user(self, username: str, password: str) -> bool:
        """用户认证"""
        if username not in self.api_keys:
            return False
        
        return self.verify_password(password, self.api_keys[username])
    
    def generate_api_key(self, username: str) -> str:
        """生成API密钥"""
        timestamp = int(time.time())
        raw_key = f"{username}_{timestamp}_{self.secret_key}"
        api_key = hashlib.sha256(raw_key.encode()).hexdigest()
        
        logger.info(f"为用户 {username} 生成API密钥")
        return api_key
    
    def validate_api_key(self, api_key: str) -> bool:
        """验证API密钥"""
        # 简单的API密钥验证（生产环境应使用数据库存储）
        valid_keys = [
            "admin_key_2025",
            "user_key_2025", 
            "test_key_2025"
        ]
        
        return api_key in valid_keys


# 全局认证管理器实例
auth_manager = AuthManager()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)):
    """获取当前用户"""
    token = credentials.credentials
    
    # 首先尝试作为JWT令牌验证
    try:
        payload = auth_manager.verify_token(token)
        return payload.get("sub")
    except HTTPException:
        pass
    
    # 然后尝试作为API密钥验证
    if auth_manager.validate_api_key(token):
        logger.info(f"API密钥认证成功: {token[:10]}...")
        return "api_user"
    
    # 认证失败
    logger.warning(f"认证失败: {token[:10]}...")
    raise HTTPException(
        status_code=401,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )


async def get_admin_user(current_user: str = Depends(get_current_user)):
    """获取管理员用户"""
    if current_user not in ["admin", "api_user"]:
        raise HTTPException(
            status_code=403,
            detail="权限不足，需要管理员权限"
        )
    return current_user


def require_auth(func):
    """认证装饰器"""
    async def wrapper(*args, **kwargs):
        # 这里可以添加额外的认证逻辑
        return await func(*args, **kwargs)
    return wrapper


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self):
        """初始化频率限制器"""
        self.requests = {}  # {ip: [(timestamp, count), ...]}
        self.max_requests_per_minute = 60
        self.max_requests_per_hour = 1000
    
    def is_allowed(self, client_ip: str) -> bool:
        """检查是否允许请求"""
        current_time = time.time()
        
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        # 清理过期记录
        self.requests[client_ip] = [
            (timestamp, count) for timestamp, count in self.requests[client_ip]
            if current_time - timestamp < 3600  # 保留1小时内的记录
        ]
        
        # 检查每分钟限制
        minute_requests = sum(
            count for timestamp, count in self.requests[client_ip]
            if current_time - timestamp < 60
        )
        
        if minute_requests >= self.max_requests_per_minute:
            logger.warning(f"IP {client_ip} 超过每分钟请求限制")
            return False
        
        # 检查每小时限制
        hour_requests = sum(
            count for timestamp, count in self.requests[client_ip]
        )
        
        if hour_requests >= self.max_requests_per_hour:
            logger.warning(f"IP {client_ip} 超过每小时请求限制")
            return False
        
        # 记录本次请求
        self.requests[client_ip].append((current_time, 1))
        return True
    
    def get_remaining_requests(self, client_ip: str) -> dict:
        """获取剩余请求次数"""
        current_time = time.time()
        
        if client_ip not in self.requests:
            return {
                "minute_remaining": self.max_requests_per_minute,
                "hour_remaining": self.max_requests_per_hour
            }
        
        # 计算已使用的请求次数
        minute_used = sum(
            count for timestamp, count in self.requests[client_ip]
            if current_time - timestamp < 60
        )
        
        hour_used = sum(
            count for timestamp, count in self.requests[client_ip]
            if current_time - timestamp < 3600
        )
        
        return {
            "minute_remaining": max(0, self.max_requests_per_minute - minute_used),
            "hour_remaining": max(0, self.max_requests_per_hour - hour_used)
        }


# 全局频率限制器实例
rate_limiter = RateLimiter()


def get_client_ip(request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 返回直连IP
    return request.client.host if request.client else "unknown"


async def check_rate_limit(request):
    """检查请求频率限制"""
    client_ip = get_client_ip(request)
    
    if not rate_limiter.is_allowed(client_ip):
        remaining = rate_limiter.get_remaining_requests(client_ip)
        raise HTTPException(
            status_code=429,
            detail="请求频率超限，请稍后重试",
            headers={
                "X-RateLimit-Remaining-Minute": str(remaining["minute_remaining"]),
                "X-RateLimit-Remaining-Hour": str(remaining["hour_remaining"]),
                "Retry-After": "60"
            }
        )
    
    return client_ip


async def verify_api_key(x_api_key: str = Depends(lambda: None)) -> str:
    """
    验证客户API密钥
    用于客户服务接口的认证
    """


    # 从Header获取API Key
    if not x_api_key:
        # 尝试从Header获取
        try:
            from fastapi import Request
            # 这里需要从请求头获取
            pass
        except:
            pass

    if not x_api_key:
        raise HTTPException(
            status_code=401,
            detail="缺少API密钥，请在请求头中添加X-API-Key"
        )

    # 验证API密钥
    auth_manager = AuthManager()
    valid_keys = auth_manager.customer_api_keys

    # 检查密钥是否有效
    customer_id = None
    for cid, key in valid_keys.items():
        if key == x_api_key:
            customer_id = cid
            break

    if not customer_id:
        logger.warning(f"无效的API密钥尝试: {x_api_key[:8]}...")
        raise HTTPException(
            status_code=401,
            detail="无效的API密钥"
        )

    logger.info(f"客户 {customer_id} API密钥验证成功")
    return customer_id


# 简化的API Key验证依赖
def verify_api_key_header(x_api_key: Optional[str] = Header(None)) -> str:
    """
    从Header验证API密钥
    """
    if not x_api_key:
        raise HTTPException(
            status_code=401,
            detail="缺少API密钥，请在请求头中添加X-API-Key"
        )

    # 验证API密钥
    auth_manager = AuthManager()
    valid_keys = auth_manager.customer_api_keys

    # 检查密钥是否有效
    customer_id = None
    for cid, key in valid_keys.items():
        if key == x_api_key:
            customer_id = cid
            break

    if not customer_id:
        logger.warning(f"无效的API密钥尝试: {x_api_key[:8]}...")
        raise HTTPException(
            status_code=401,
            detail="无效的API密钥"
        )

    logger.info(f"客户 {customer_id} API密钥验证成功")
    return customer_id
