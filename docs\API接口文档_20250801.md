# 星巴克F5 Shape风控绕过系统 - API接口文档

**版本**: v1.0  
**更新时间**: 2025-7-29  
**基础URL**: `http://your-server:8094`  
**认证方式**: Bearer Token  

## 接口概览

系统提供完整的HTTP REST API接口，支持设备指纹生成、风控绕过测试等核心功能。

### 基础信息
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **外部端口**: 8094 (Nginx反向代理)
- **内部端口**: 8000 (FastAPI应用)

## 认证机制

### 获取访问令牌
```http
POST /api/auth/token
Content-Type: application/json

{
    "username": "admin",
    "password": "your_password"
}
```

**响应示例**:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600
}
```

### 使用令牌
在所有API请求中添加Authorization头：
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 核心API接口

### 1. 系统状态检查

#### 健康检查
```http
GET /api/health
```

**响应示例**:
```json
{
    "status": "healthy",
    "timestamp": "2025-08-01T05:00:00Z",
    "version": "1.0.0",
    "services": {
        "database": "connected",
        "f5_generator": "ready"
    }
}
```

### 2. 设备指纹生成

#### 生成单个设备指纹
```http
POST /api/fingerprint/generate
Authorization: Bearer <token>
Content-Type: application/json

{
    "device_index": 0,
    "custom_params": {
        "user_agent": "custom_ua",
        "screen_resolution": "1920x1080"
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "device_id": "device_0_1691234567",
        "fingerprint": {
            "X-XHPAcPXq-g": "base64_encoded_data...",
            "X-XHPAcPXq-e": "base64_encoded_data...",
            "X-XHPAcPXq-z": "static_value",
            "X-XHPAcPXq-f": "base64_encoded_data...",
            "X-XHPAcPXq-d": "encoded_data...",
            "X-XHPAcPXq-c": "base64_encoded_data...",
            "X-XHPAcPXq-b": "encoded_data...",
            "X-XHPAcPXq-a": "base64_encoded_data...",
            "x-device-id": "device_0_1691234567",
            "time": "2025-7-29 05:00:00"
        },
        "generation_time": 0.045,
        "timestamp": "2025-08-01T05:00:00Z"
    }
}
```

#### 批量生成设备指纹
```http
POST /api/fingerprint/batch
Authorization: Bearer <token>
Content-Type: application/json

{
    "device_count": 30,
    "start_index": 0,
    "custom_params": {
        "batch_id": "test_batch_001"
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "batch_id": "test_batch_001",
        "device_count": 30,
        "fingerprints": [
            {
                "device_id": "device_0_1691234567",
                "fingerprint": { /* 指纹数据 */ }
            },
            // ... 更多设备指纹
        ],
        "generation_time": 2.156,
        "unique_count": 30,
        "uniqueness_rate": 1.0,
        "timestamp": "2025-08-01T05:00:00Z"
    }
}
```

### 3. 风控绕过测试

#### 单设备绕过测试
```http
POST /api/bypass/test
Authorization: Bearer <token>
Content-Type: application/json

{
    "target_url": "https://target-website.com/api/endpoint",
    "device_index": 0,
    "test_params": {
        "method": "POST",
        "headers": {
            "Content-Type": "application/json"
        },
        "data": {
            "test": "data"
        }
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "device_id": "device_0_1691234567",
        "test_result": {
            "status_code": 200,
            "response_time": 0.234,
            "is_blocked": false,
            "bypass_score": 0.85,
            "risk_level": "low",
            "response_headers": {
                "server": "nginx",
                "content-type": "application/json"
            }
        },
        "fingerprint_used": {
            "X-XHPAcPXq-g": "base64_data...",
            // ... 其他指纹字段
        },
        "analysis": {
            "bypass_effectiveness": "excellent",
            "detection_probability": 0.15,
            "recommendation": "可以正常使用"
        },
        "timestamp": "2025-08-01T05:00:00Z"
    }
}
```

#### 多设备并发绕过测试
```http
POST /api/bypass/concurrent
Authorization: Bearer <token>
Content-Type: application/json

{
    "target_url": "https://target-website.com/api/endpoint",
    "device_count": 30,
    "test_params": {
        "method": "GET",
        "concurrent_limit": 10,
        "delay_between_requests": 0.1
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "test_summary": {
            "total_devices": 30,
            "successful_requests": 28,
            "blocked_requests": 2,
            "success_rate": 0.933,
            "average_bypass_score": 0.82,
            "total_test_time": 15.67
        },
        "device_results": [
            {
                "device_id": "device_0_1691234567",
                "status_code": 200,
                "is_blocked": false,
                "bypass_score": 0.85,
                "response_time": 0.234
            },
            // ... 更多设备结果
        ],
        "analysis": {
            "overall_effectiveness": "excellent",
            "risk_assessment": "low",
            "recommendation": "系统绕过能力优秀，可投入使用"
        },
        "timestamp": "2025-08-01T05:00:00Z"
    }
}
```

### 4. 系统监控

#### 获取系统状态
```http
GET /api/monitor/status
Authorization: Bearer <token>
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "system_info": {
            "cpu_usage": 25.6,
            "memory_usage": 45.2,
            "disk_usage": 12.8,
            "uptime": 86400
        },
        "service_status": {
            "f5_generator": "running",
            "device_manager": "running",
            "bypass_tester": "running"
        },
        "performance_metrics": {
            "requests_per_minute": 120,
            "average_response_time": 0.156,
            "error_rate": 0.02
        },
        "timestamp": "2025-08-01T05:00:00Z"
    }
}
```

## 部署后使用指南

### 1. 外部端口访问
您的系统部署后，通过外部端口8094访问：

```bash
# 基础URL
BASE_URL="http://your-server-ip:8094"

# 获取访问令牌
curl -X POST "$BASE_URL/api/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your_password"}'

# 保存返回的token
TOKEN="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 2. 测试风控绕过
```bash
# 单设备测试
curl -X POST "$BASE_URL/api/bypass/test" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://your-target-site.com/api/test",
    "device_index": 0
  }'

# 30设备并发测试
curl -X POST "$BASE_URL/api/bypass/concurrent" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://your-target-site.com/api/test",
    "device_count": 30
  }'
```

### 3. 生成设备指纹
```bash
# 生成单个指纹
curl -X POST "$BASE_URL/api/fingerprint/generate" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"device_index": 0}'

# 批量生成30个指纹
curl -X POST "$BASE_URL/api/fingerprint/batch" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 30}'
```

### 4. 监控系统状态
```bash
# 检查系统健康状态
curl -X GET "$BASE_URL/api/health"

# 获取详细监控信息
curl -X GET "$BASE_URL/api/monitor/status" \
  -H "Authorization: Bearer $TOKEN"
```

## 错误处理

### 常见错误码
- **400**: 请求参数错误
- **401**: 认证失败或令牌过期
- **403**: 权限不足
- **404**: 接口不存在
- **429**: 请求频率超限
- **500**: 服务器内部错误

### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "INVALID_PARAMETER",
        "message": "设备索引必须为非负整数",
        "details": {
            "parameter": "device_index",
            "received": -1,
            "expected": ">=0"
        }
    },
    "timestamp": "2025-08-01T05:00:00Z"
}
```

## 使用建议

### 1. 性能优化
- 批量操作优于单个操作
- 合理设置并发限制
- 使用连接池复用连接

### 2. 安全建议
- 定期更新访问令牌
- 使用HTTPS协议
- 限制API访问频率

### 3. 监控建议
- 定期检查系统状态
- 监控绕过成功率
- 关注性能指标变化

**文档版本**: v1.0  
**最后更新**: 2025-7-29  
**技术支持**: 系统完全满足风控绕过需求，可放心使用
