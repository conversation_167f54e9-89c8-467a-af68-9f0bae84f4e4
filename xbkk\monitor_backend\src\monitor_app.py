#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
星巴克风控绕过系统 - 独立监控后台
专用于监控所有客户接口使用情况，防止客户装后门捣乱
"""

import os
import json
import sqlite3
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, Form
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import uvicorn
import aiofiles
import aiohttp
from cryptography.fernet import Fernet
import logging
import jwt
import secrets

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/monitor_backend.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="星巴克风控绕过系统 - 监控后台",
    description="独立监控后台，防止客户装后门捣乱",
    version="1.0.0"
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="./src/static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="./src/templates")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全配置
security = HTTPBearer()

# 加密密钥
ENCRYPTION_KEY = os.getenv('MONITOR_ENCRYPTION_KEY', Fernet.generate_key())
if isinstance(ENCRYPTION_KEY, str):
    ENCRYPTION_KEY = ENCRYPTION_KEY.encode()
cipher_suite = Fernet(ENCRYPTION_KEY)

# JWT配置
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', secrets.token_urlsafe(32))
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = int(os.getenv('JWT_EXPIRE_HOURS', '24'))

# 固定管理员账户
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME', 'admin')
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123456')

# API认证令牌
MONITOR_BACKEND_TOKEN = os.getenv('MONITOR_BACKEND_TOKEN', 'monitor_backend_secret_token_2025')

# 数据模型
class LogEntry(BaseModel):
    timestamp: str
    client_ip: str
    customer_id: str
    user_type: Optional[str] = "customer"  # customer, admin, anonymous, internal
    api_endpoint: str
    request_method: str
    request_headers: Dict[str, Any]
    request_body: Optional[str] = None
    response_status: int
    response_body: Optional[str] = None
    response_time: float
    user_agent: str
    is_suspicious: bool = False
    risk_score: float = 0.0

class CleanupRequest(BaseModel):
    customer_id: Optional[str] = None
    confirm_deletion: bool = False

class AlertConfig(BaseModel):
    email_enabled: bool = True
    webhook_enabled: bool = True
    threshold_requests_per_minute: int = 100
    threshold_error_rate: float = 0.1

class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class LoginResponse(BaseModel):
    success: bool
    message: str
    access_token: Optional[str] = None
    token_type: str = "bearer"

# 数据库管理器
class MonitorDatabase:
    def __init__(self, db_path: str = "./monitor_logs.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                client_ip TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                user_type TEXT DEFAULT 'customer',
                api_endpoint TEXT NOT NULL,
                request_method TEXT NOT NULL,
                request_headers TEXT NOT NULL,
                request_body TEXT,
                response_status INTEGER NOT NULL,
                response_body TEXT,
                response_time REAL NOT NULL,
                user_agent TEXT NOT NULL,
                is_suspicious BOOLEAN DEFAULT FALSE,
                risk_score REAL DEFAULT 0.0,
                encrypted_data TEXT NOT NULL
            )
        ''')

        # 添加user_type字段（如果不存在）
        try:
            cursor.execute('ALTER TABLE api_logs ADD COLUMN user_type TEXT DEFAULT "customer"')
        except sqlite3.OperationalError:
            # 字段已存在，忽略错误
            pass
        
        # 创建客户统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer_stats (
                customer_id TEXT PRIMARY KEY,
                total_requests INTEGER DEFAULT 0,
                total_errors INTEGER DEFAULT 0,
                last_access TEXT,
                risk_level TEXT DEFAULT 'LOW',
                is_blocked BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # 创建告警表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                customer_id TEXT,
                message TEXT NOT NULL,
                severity TEXT NOT NULL,
                is_resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def encrypt_data(self, data: str) -> str:
        """加密敏感数据"""
        return cipher_suite.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        return cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def log_api_request(self, log_entry: LogEntry):
        """记录API请求日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 加密敏感数据
        sensitive_data = {
            'request_headers': log_entry.request_headers,
            'request_body': log_entry.request_body,
            'response_body': log_entry.response_body
        }
        encrypted_data = self.encrypt_data(json.dumps(sensitive_data))
        
        cursor.execute('''
            INSERT INTO api_logs (
                timestamp, client_ip, customer_id, user_type, api_endpoint, request_method,
                request_headers, request_body, response_status, response_body,
                response_time, user_agent, is_suspicious, risk_score, encrypted_data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            log_entry.timestamp, log_entry.client_ip, log_entry.customer_id,
            getattr(log_entry, 'user_type', 'customer'),
            log_entry.api_endpoint, log_entry.request_method,
            json.dumps(log_entry.request_headers), log_entry.request_body,
            log_entry.response_status, log_entry.response_body,
            log_entry.response_time, log_entry.user_agent,
            log_entry.is_suspicious, log_entry.risk_score, encrypted_data
        ))
        
        conn.commit()
        conn.close()
        
        # 更新客户统计
        self.update_customer_stats(log_entry.customer_id, log_entry.response_status >= 400)
    
    def update_customer_stats(self, customer_id: str, is_error: bool):
        """更新客户统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO customer_stats (
                customer_id, total_requests, total_errors, last_access
            ) VALUES (
                ?, 
                COALESCE((SELECT total_requests FROM customer_stats WHERE customer_id = ?), 0) + 1,
                COALESCE((SELECT total_errors FROM customer_stats WHERE customer_id = ?), 0) + ?,
                ?
            )
        ''', (customer_id, customer_id, customer_id, 1 if is_error else 0, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def get_logs(self, limit: int = 100, customer_id: Optional[str] = None) -> List[Dict]:
        """获取日志记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
            SELECT * FROM api_logs 
            WHERE (? IS NULL OR customer_id = ?)
            ORDER BY timestamp DESC 
            LIMIT ?
        '''
        
        cursor.execute(query, (customer_id, customer_id, limit))
        rows = cursor.fetchall()
        
        columns = [description[0] for description in cursor.description]
        logs = []
        
        for row in rows:
            log_dict = dict(zip(columns, row))
            # 解密敏感数据
            try:
                encrypted_data = log_dict.get('encrypted_data', '{}')
                decrypted_data = json.loads(self.decrypt_data(encrypted_data))
                log_dict.update(decrypted_data)
            except:
                pass
            logs.append(log_dict)
        
        conn.close()
        return logs
    
    def get_customer_stats(self) -> List[Dict]:
        """获取客户统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM customer_stats ORDER BY total_requests DESC')
        rows = cursor.fetchall()
        
        columns = [description[0] for description in cursor.description]
        stats = [dict(zip(columns, row)) for row in rows]
        
        conn.close()
        return stats
    
    def cleanup_data(self, customer_id: Optional[str] = None) -> Dict[str, int]:
        """清理数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if customer_id:
            # 清理特定客户数据
            cursor.execute('DELETE FROM api_logs WHERE customer_id = ?', (customer_id,))
            logs_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM customer_stats WHERE customer_id = ?', (customer_id,))
            stats_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM alerts WHERE customer_id = ?', (customer_id,))
            alerts_deleted = cursor.rowcount
        else:
            # 清理所有数据
            cursor.execute('DELETE FROM api_logs')
            logs_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM customer_stats')
            stats_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM alerts')
            alerts_deleted = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            'logs_deleted': logs_deleted,
            'stats_deleted': stats_deleted,
            'alerts_deleted': alerts_deleted
        }

    def create_alert(self, alert_type: str, customer_id: str, message: str, severity: str):
        """创建告警"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO alerts (timestamp, alert_type, customer_id, message, severity)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            alert_type,
            customer_id,
            message,
            severity
        ))

        conn.commit()
        conn.close()

# 全局数据库实例
db = MonitorDatabase()

# 安全检查器
class SecurityChecker:
    def __init__(self):
        self.suspicious_patterns = [
            'eval(',
            'exec(',
            'import os',
            'subprocess',
            '__import__',
            'open(',
            'file(',
            'input(',
            'raw_input(',
            'compile(',
            'reload(',
            'getattr(',
            'setattr(',
            'delattr(',
            'hasattr(',
            'globals(',
            'locals(',
            'vars(',
            'dir(',
            'help(',
            'copyright',
            'credits',
            'license',
            'quit',
            'exit',
            'sys.exit',
            'os.system',
            'os.popen',
            'os.spawn',
            'os.fork',
            'os.kill',
            'socket.',
            'urllib',
            'requests.',
            'http.',
            'ftp.',
            'telnet.',
            'ssh.',
            'paramiko.',
            'fabric.',
            'ansible.',
            'docker.',
            'kubernetes.',
            'kubectl.',
            'bash',
            'sh',
            'cmd',
            'powershell',
            'wget',
            'curl',
            'nc',
            'netcat',
            'nmap',
            'sqlmap',
            'metasploit',
            'msfconsole',
            'msfvenom',
            'hydra',
            'john',
            'hashcat',
            'aircrack',
            'wireshark',
            'tcpdump',
            'nessus',
            'openvas',
            'nikto',
            'dirb',
            'gobuster',
            'wfuzz',
            'burp',
            'zap',
            'sqlinjection',
            'xss',
            'csrf',
            'lfi',
            'rfi',
            'xxe',
            'ssrf',
            'rce',
            'backdoor',
            'webshell',
            'reverse_shell',
            'bind_shell',
            'payload',
            'exploit',
            'vulnerability',
            'pentest',
            'hack',
            'crack',
            'bypass',
            'injection',
            'overflow',
            'privilege',
            'escalation',
            'persistence',
            'lateral',
            'movement',
            'exfiltration',
            'c2',
            'command_control',
            'botnet',
            'malware',
            'trojan',
            'virus',
            'worm',
            'rootkit',
            'keylogger',
            'ransomware',
            'cryptominer',
            'steganography',
            'covert',
            'channel',
            'tunnel',
            'proxy',
            'socks',
            'tor',
            'onion',
            'darkweb',
            'anonymous',
            'vpn',
            'encryption',
            'decryption',
            'cipher',
            'hash',
            'salt',
            'rainbow',
            'dictionary',
            'bruteforce',
            'wordlist',
            'rockyou',
            'kali',
            'parrot',
            'blackarch',
            'pentoo',
            'backtrack'
        ]
    
    def check_request(self, request_data: str) -> tuple[bool, float]:
        """检查请求是否可疑"""
        if not request_data:
            return False, 0.0
        
        request_lower = request_data.lower()
        suspicious_count = 0
        
        for pattern in self.suspicious_patterns:
            if pattern in request_lower:
                suspicious_count += 1
        
        risk_score = min(suspicious_count / 10.0, 1.0)
        is_suspicious = risk_score > 0.3
        
        return is_suspicious, risk_score

# 全局安全检查器
security_checker = SecurityChecker()

# JWT工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRE_HOURS)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_jwt_token(token: str):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return username
    except jwt.PyJWTError:
        return None

# 认证依赖
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    token = credentials.credentials

    # 检查是否是监控后台令牌
    if token == MONITOR_BACKEND_TOKEN:
        return token

    # 检查是否是JWT令牌
    username = verify_jwt_token(token)
    if username is not None:
        return token

    raise HTTPException(status_code=401, detail="无效的访问令牌")

    return token

async def verify_web_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证Web界面JWT令牌"""
    token = credentials.credentials
    username = verify_jwt_token(token)

    if username is None:
        raise HTTPException(status_code=401, detail="无效的访问令牌")

    return username

def authenticate_user(username: str, password: str) -> bool:
    """验证用户凭据"""
    return username == ADMIN_USERNAME and password == ADMIN_PASSWORD

# Web界面路由
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路径重定向到登录页面"""
    return RedirectResponse(url="/login")

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard_page(request: Request):
    """管理后台页面"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.post("/api/auth/login")
async def login(login_data: LoginRequest):
    """用户登录"""
    if not authenticate_user(login_data.username, login_data.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 创建访问令牌
    access_token_expires = timedelta(hours=JWT_EXPIRE_HOURS)
    access_token = create_access_token(
        data={"sub": login_data.username},
        expires_delta=access_token_expires
    )

    return LoginResponse(
        success=True,
        message="登录成功",
        access_token=access_token,
        token_type="bearer"
    )

@app.get("/api/auth/verify")
async def verify_auth(username: str = Depends(verify_web_token)):
    """验证用户认证状态"""
    return {
        "success": True,
        "username": username,
        "message": "认证有效"
    }

@app.post("/api/auth/logout")
async def logout():
    """用户登出"""
    return {
        "success": True,
        "message": "登出成功"
    }

# API路由
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "monitor_backend",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/api/log")
async def log_api_request(log_entry: LogEntry, token: str = Depends(verify_token)):
    """记录API请求日志"""
    try:
        # 安全检查
        request_data = f"{log_entry.request_body} {json.dumps(log_entry.request_headers)}"
        is_suspicious, risk_score = security_checker.check_request(request_data)

        log_entry.is_suspicious = is_suspicious
        log_entry.risk_score = risk_score

        # 记录日志
        db.log_api_request(log_entry)

        # 如果检测到可疑行为，发送告警
        if is_suspicious:
            logger.warning(f"检测到可疑请求 - 客户: {log_entry.customer_id}, 风险评分: {risk_score}")

        return {
            "success": True,
            "message": "日志记录成功",
            "is_suspicious": is_suspicious,
            "risk_score": risk_score
        }

    except Exception as e:
        logger.error(f"记录日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"记录日志失败: {str(e)}")

@app.post("/api/logs/record")
async def record_customer_log(log_entry: LogEntry, token: str = Depends(verify_token)):
    """专用于主系统记录客户日志的端点"""
    try:
        # 安全检查
        request_data = f"{log_entry.request_body} {json.dumps(log_entry.request_headers)}"
        is_suspicious, risk_score = security_checker.check_request(request_data)

        log_entry.is_suspicious = is_suspicious
        log_entry.risk_score = risk_score

        # 记录日志
        db.log_api_request(log_entry)

        # 更新客户统计
        db.update_customer_stats(log_entry.customer_id, log_entry.response_status >= 400)

        # 如果检测到可疑行为，发送告警
        if is_suspicious:
            logger.warning(f"客户可疑行为检测 - 客户: {log_entry.customer_id}, "
                         f"IP: {log_entry.client_ip}, 风险评分: {risk_score}")

            # 记录告警
            alert_message = f"客户 {log_entry.customer_id} 存在可疑行为，风险评分: {risk_score}"
            db.create_alert("SUSPICIOUS_BEHAVIOR", log_entry.customer_id, alert_message, "WARNING")

        return {
            "success": True,
            "message": "客户日志记录成功",
            "is_suspicious": is_suspicious,
            "risk_score": risk_score
        }

    except Exception as e:
        logger.error(f"记录客户日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"记录客户日志失败: {str(e)}")

@app.get("/api/logs")
async def get_logs(
    limit: int = 100,
    customer_id: Optional[str] = None,
    token: str = Depends(verify_token)
):
    """获取日志记录"""
    try:
        logs = db.get_logs(limit=limit, customer_id=customer_id)
        return {
            "success": True,
            "data": logs,
            "total": len(logs)
        }
    
    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")

@app.get("/api/stats/customers")
async def get_customer_stats(token: str = Depends(verify_token)):
    """获取客户统计信息"""
    try:
        stats = db.get_customer_stats()
        return {
            "success": True,
            "total_requests": stats.get("total_requests", 0),
            "active_customers": stats.get("active_customers", 0),
            "suspicious_requests": stats.get("suspicious_requests", 0),
            "avg_response_time": stats.get("avg_response_time", 0),
            "data": stats,
            "total": len(stats) if isinstance(stats, list) else 0
        }

    except Exception as e:
        logger.error(f"获取客户统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取客户统计失败: {str(e)}")

@app.get("/api/system/status")
async def get_system_status(username: str = Depends(verify_web_token)):
    """获取系统状态信息"""
    try:
        import psutil
        import time

        # 获取CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)

        # 获取内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent

        # 获取磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = (disk.used / disk.total) * 100

        # 获取系统运行时间
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)
        uptime = f"{uptime_hours}小时{uptime_minutes}分钟"

        return {
            "success": True,
            "cpu_usage": round(cpu_usage, 1),
            "memory_usage": round(memory_usage, 1),
            "disk_usage": round(disk_usage, 1),
            "uptime": uptime,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@app.post("/api/cleanup")
async def cleanup_data(
    cleanup_request: CleanupRequest,
    token: str = Depends(verify_token)
):
    """清理数据 - 即用即删功能"""
    try:
        if not cleanup_request.confirm_deletion:
            raise HTTPException(status_code=400, detail="必须确认删除操作")
        
        result = db.cleanup_data(customer_id=cleanup_request.customer_id)
        
        logger.info(f"数据清理完成 - 客户: {cleanup_request.customer_id or '全部'}, 结果: {result}")
        
        return {
            "success": True,
            "message": "数据清理完成",
            "result": result
        }
    
    except Exception as e:
        logger.error(f"数据清理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")

@app.post("/api/system/reset")
async def system_reset(token: str = Depends(verify_token)):
    """系统重置 - 彻底清理所有数据"""
    try:
        # 清理数据库
        result = db.cleanup_data()
        
        # 清理日志文件
        log_files = ['./logs/monitor_backend.log']
        for log_file in log_files:
            if os.path.exists(log_file):
                os.remove(log_file)
        
        logger.info("系统重置完成")
        
        return {
            "success": True,
            "message": "系统重置完成",
            "result": result
        }
    
    except Exception as e:
        logger.error(f"系统重置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统重置失败: {str(e)}")

@app.get("/api/alerts/anomalies")
async def get_anomalies(token: str = Depends(verify_token)):
    """获取异常报告"""
    try:
        # 获取可疑日志
        logs = db.get_logs(limit=1000)
        suspicious_logs = [log for log in logs if log.get('is_suspicious', False)]
        
        # 统计异常情况
        anomalies = {
            "total_suspicious_requests": len(suspicious_logs),
            "high_risk_customers": [],
            "suspicious_patterns": {},
            "recent_alerts": suspicious_logs[:10]
        }
        
        # 分析高风险客户
        customer_risks = {}
        for log in suspicious_logs:
            customer_id = log.get('customer_id', 'unknown')
            if customer_id not in customer_risks:
                customer_risks[customer_id] = []
            customer_risks[customer_id].append(log.get('risk_score', 0))
        
        for customer_id, risks in customer_risks.items():
            avg_risk = sum(risks) / len(risks)
            if avg_risk > 0.5:
                anomalies["high_risk_customers"].append({
                    "customer_id": customer_id,
                    "average_risk_score": avg_risk,
                    "suspicious_requests": len(risks)
                })
        
        return {
            "success": True,
            "data": anomalies
        }
    
    except Exception as e:
        logger.error(f"获取异常报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取异常报告失败: {str(e)}")

if __name__ == "__main__":
    # 创建日志目录
    os.makedirs('./logs', exist_ok=True)
    
    # 启动监控后台
    uvicorn.run(
        app,
        host=os.getenv('MONITOR_HOST', '0.0.0.0'),
        port=int(os.getenv('MONITOR_PORT', 9000)),
        log_level="info"
    )
