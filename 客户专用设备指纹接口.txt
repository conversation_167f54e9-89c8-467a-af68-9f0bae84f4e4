# 设备指纹生成接口 - 客户专用

## 接口信息
服务器地址: http://38.150.2.100:8094
API密钥: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS

## 核心接口（唯一需要的接口）

### 生成设备指纹
POST /api/v1/fingerprint/generate

请求头:
X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
Content-Type: application/json

请求参数:
{
  "device_count": 30,        // 生成指纹数量，最大30个
  "force_regenerate": true   // 强制生成新指纹，确保每次都不同
}

## 使用示例

### 1. 生成30个不同指纹（高并发）
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 30, "force_regenerate": true}'

### 2. 生成单个指纹
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 1, "force_regenerate": true}'

## 返回数据格式

成功响应:
{
  "success": true,
  "message": "成功生成30个设备指纹",
  "fingerprints": [
    {
      "x-device-id": "A1B2C3D4-E5F6-7890-ABCD-EF1234567890",
      "X-XHPAcPXq-z": "固定值",
      "X-XHPAcPXq-g": "动态生成的长字符串...",
      "X-XHPAcPXq-e": "动态值",
      "X-XHPAcPXq-f": "固定值",
      "X-XHPAcPXq-d": "固定值", 
      "X-XHPAcPXq-c": "固定值",
      "X-XHPAcPXq-b": "固定值",
      "X-XHPAcPXq-a": "动态值",
      "Authorization": "Bearer 动态token",
      "x-bs-device-id": "动态设备ID",
      "time": "2025-08-01 15:30:45"
    }
    // ... 更多指纹
  ]
}

## 如何使用指纹进行下单

获取指纹后，将所有字段作为HTTP请求头发送给目标网站:

示例代码（Python）:
```python
import requests

# 1. 获取指纹
response = requests.post(
    "http://38.150.2.100:8094/api/v1/fingerprint/generate",
    headers={
        "X-API-Key": "SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS",
        "Content-Type": "application/json"
    },
    json={"device_count": 1, "force_regenerate": True}
)

fingerprint = response.json()["fingerprints"][0]

# 2. 使用指纹访问目标网站
target_headers = {
    "x-device-id": fingerprint["x-device-id"],
    "X-XHPAcPXq-z": fingerprint["X-XHPAcPXq-z"],
    "X-XHPAcPXq-g": fingerprint["X-XHPAcPXq-g"],
    "X-XHPAcPXq-e": fingerprint["X-XHPAcPXq-e"],
    "X-XHPAcPXq-f": fingerprint["X-XHPAcPXq-f"],
    "X-XHPAcPXq-d": fingerprint["X-XHPAcPXq-d"],
    "X-XHPAcPXq-c": fingerprint["X-XHPAcPXq-c"],
    "X-XHPAcPXq-b": fingerprint["X-XHPAcPXq-b"],
    "X-XHPAcPXq-a": fingerprint["X-XHPAcPXq-a"],
    "Authorization": fingerprint["Authorization"],
    "x-bs-device-id": fingerprint["x-bs-device-id"],
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}

# 3. 发送下单请求
order_response = requests.post(
    "https://目标网站.com/api/order",
    headers=target_headers,
    json={"product_id": "123", "quantity": 1}
)
```

## 核心特性

✅ 每次生成的指纹都完全不同
✅ 支持高并发（最多30个同时）
✅ 能够绕过F5 Shape风控
✅ 响应速度快（<100ms）
✅ 真实可用于下单
✅ 100%唯一性保证

## 重要说明

1. 每次调用都会生成全新的指纹，确保不重复
2. 建议每次下单前都重新获取指纹
3. 支持高并发，可以同时获取多个指纹
4. 所有指纹字段都需要添加到HTTP请求头中
5. 指纹有效期建议不超过10分钟

## 技术支持

如有问题请联系技术支持，提供具体的错误信息和使用场景。
