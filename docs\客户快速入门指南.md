# 星巴克风控绕过系统 - 客户快速入门指南

## 5分钟快速上手

### 第一步：获取信息
- **服务器地址**: `http://您的服务器IP:8094`
- **API密钥**: 联系技术支持获取

### 第二步：最简单的测试
复制以下命令，替换服务器IP和API密钥后执行：

```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{"target_url": "https://www.starbucks.com.cn"}'
```

### 第三步：查看结果
成功的话会返回类似这样的结果：
```json
{
  "success": true,
  "message": "风控绕过测试完成",
  "data": {
    "success_rate": 85.6,
    "bypass_effectiveness": "良好",
    "total_requests": 25,
    "successful_requests": 21
  }
}
```

---

## 常用测试场景

### 场景1：基础测试（推荐新手）
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{
    "target_url": "https://目标网站.com"
  }'
```

### 场景2：高强度测试
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{
    "target_url": "https://目标网站.com",
    "test_config": {
      "device_count": 15,
      "concurrent_limit": 8
    }
  }'
```

### 场景3：温和测试（避免被发现）
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 您的API密钥" \
  -d '{
    "target_url": "https://目标网站.com",
    "test_config": {
      "device_count": 3,
      "delay_between_requests": 2.0
    }
  }'
```

---

## 使用Postman测试（图形界面）

### 步骤1：设置请求
1. 打开Postman
2. 新建POST请求
3. URL填入：`http://您的服务器IP:8094/api/bypass/test-service`

### 步骤2：设置请求头
在Headers标签页添加：
- Key: `Content-Type`, Value: `application/json`
- Key: `X-API-Key`, Value: `您的API密钥`

### 步骤3：设置请求体
在Body标签页选择raw和JSON，填入：
```json
{
  "target_url": "https://www.starbucks.com.cn"
}
```

### 步骤4：发送请求
点击Send按钮，查看返回结果。

---

## 结果解读

### 成功率说明
- **90%以上**: 绕过效果优秀，风控基本失效
- **70-90%**: 绕过效果良好，大部分请求成功
- **50-70%**: 绕过效果一般，建议调整参数
- **50%以下**: 绕过效果较差，需要优化策略

### 绕过效果等级
- **优秀**: 风控完全失效，可大规模使用
- **良好**: 风控大幅降低，可正常使用
- **一般**: 风控有所降低，建议小规模使用
- **较差**: 风控依然有效，需要调整策略

---

## 常见错误及解决

### 错误1：API密钥无效
```json
{
  "success": false,
  "message": "无效的API密钥",
  "error_code": "AUTH_001"
}
```
**解决**: 检查API密钥是否正确，联系技术支持确认。

### 错误2：参数格式错误
```json
{
  "success": false,
  "message": "请求参数格式错误",
  "error_code": "PARAM_001"
}
```
**解决**: 检查JSON格式是否正确，确保Content-Type为application/json。

### 错误3：目标URL无效
```json
{
  "success": false,
  "message": "目标URL格式错误",
  "error_code": "TARGET_001"
}
```
**解决**: 确保URL以http://或https://开头，格式正确。

---

## 进阶参数说明

### device_count（设备数量）
- **范围**: 1-30
- **建议**: 5-15台
- **说明**: 模拟的设备数量，越多绕过效果越好，但也更容易被检测

### concurrent_limit（并发限制）
- **范围**: 1-设备数量
- **建议**: 设备数量的30-50%
- **说明**: 同时发送请求的数量，过高可能被限流

### delay_between_requests（请求间隔）
- **范围**: 0.1-10.0秒
- **建议**: 0.5-2.0秒
- **说明**: 每次请求之间的间隔，模拟真实用户行为

### method（请求方法）
- **选项**: GET、POST
- **默认**: GET
- **说明**: HTTP请求方法，根据目标接口选择

---

## 技术支持

### 获取帮助
- 遇到问题请联系技术支持
- 提供详细的错误信息和请求参数
- 说明您的使用场景和目标

### 优化建议
- 首次使用建议从基础测试开始
- 根据结果逐步调整参数
- 不同网站可能需要不同的策略

### 注意事项
- 请在授权范围内使用
- 避免对同一目标频繁测试
- 遵守相关法律法规

---

**开始您的第一次测试吧！如有任何问题，随时联系技术支持。**
