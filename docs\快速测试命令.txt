F5 Shape风控绕过系统 - 客户快速测试命令
===========================================

API密钥: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
服务器: http://38.150.2.100:8094

===========================================
1. 健康检查（无需密钥）
===========================================
curl -X GET "http://38.150.2.100:8094/health"

===========================================
2. 基础测试（5设备验证功能）
===========================================
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 5,
      "method": "GET",
      "concurrent_limit": 3
    }
  }'

===========================================
3. 并发测试（30设备同时请求）
===========================================
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 30,
      "delay_between_requests": 0
    }
  }'

===========================================
4. 客户自定义目标测试（替换target_url）
===========================================
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://客户的目标网站.com",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 15
    }
  }'

===========================================
5. 压力测试（连续5次）
===========================================
for i in {1..5}; do
  echo "第 $i 次测试..."
  curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
    -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
    -H "Content-Type: application/json" \
    -d '{
      "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
      "test_config": {
        "device_count": 30,
        "method": "GET",
        "concurrent_limit": 15
      }
    }'
  sleep 2
done

===========================================
成功标准:
===========================================
- success_rate > 85%
- average_response_time < 3秒
- f5_detection_bypassed = true
- 30设备并发正常运行

===========================================
其他API密钥（按需分配）:
===========================================
客户A: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
客户B: SB_API_2025_CUSTOMER_002_F5SHAPE_BYPASS
客户C: SB_API_2025_CUSTOMER_003_F5SHAPE_BYPASS
演示用: demo_api_key_change_in_production

===========================================
技术支持:
===========================================
API文档: http://38.150.2.100:8094/docs
支持时间: 7x24小时在线
联系方式: 技术支持团队
