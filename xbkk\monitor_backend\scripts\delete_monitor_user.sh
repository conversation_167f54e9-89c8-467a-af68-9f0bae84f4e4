#!/bin/bash

# 星巴克风控绕过系统 - 监控后台用户删除脚本
# 作者：YINGAshadow
# 创建时间：2025-8-1
# 功能：完全删除监控后台用户和所有相关文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_USER="monitor"
USER_HOME="/home/<USER>"
SERVICE_NAME="monitor-backend"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        log_error "使用命令: sudo $0"
        exit 1
    fi
}

# 确认删除
confirm_deletion() {
    log_step "确认删除操作"
    
    echo ""
    log_warn "此操作将完全删除监控后台用户和所有相关数据！"
    log_warn "包括："
    echo "  - 用户账户: $DEPLOY_USER"
    echo "  - 用户主目录: $USER_HOME"
    echo "  - 系统服务: $SERVICE_NAME"
    echo "  - 所有项目文件和数据"
    echo ""
    
    read -p "确认删除？(输入 'DELETE' 确认): " confirmation
    
    if [[ "$confirmation" != "DELETE" ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "确认删除，开始清理..."
}

# 停止服务
stop_services() {
    log_step "停止监控后台服务"
    
    # 停止systemd服务
    if systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
        systemctl stop "$SERVICE_NAME"
        log_info "已停止服务: $SERVICE_NAME"
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet "$SERVICE_NAME" 2>/dev/null; then
        systemctl disable "$SERVICE_NAME"
        log_info "已禁用服务: $SERVICE_NAME"
    fi
    
    # 删除服务文件
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        log_info "已删除服务文件"
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
}

# 删除Nginx配置
remove_nginx_config() {
    log_step "删除Nginx配置"
    
    # 删除站点配置
    if [[ -f "/etc/nginx/sites-available/monitor-backend" ]]; then
        rm -f "/etc/nginx/sites-available/monitor-backend"
        log_info "已删除Nginx站点配置"
    fi
    
    if [[ -L "/etc/nginx/sites-enabled/monitor-backend" ]]; then
        rm -f "/etc/nginx/sites-enabled/monitor-backend"
        log_info "已删除Nginx站点链接"
    fi
    
    # 测试Nginx配置
    if nginx -t 2>/dev/null; then
        systemctl reload nginx
        log_info "已重新加载Nginx配置"
    else
        log_warn "Nginx配置测试失败，请手动检查"
    fi
}

# 关闭防火墙端口
close_firewall_ports() {
    log_step "关闭防火墙端口"
    
    # 关闭监控后台端口
    if ufw status | grep -q "9094"; then
        ufw delete allow 9094/tcp 2>/dev/null || true
        log_info "已关闭端口: 9094"
    fi
    
    if ufw status | grep -q "9000"; then
        ufw delete allow 9000/tcp 2>/dev/null || true
        log_info "已关闭端口: 9000"
    fi
}

# 终止用户进程
kill_user_processes() {
    log_step "终止用户进程"
    
    if id "$DEPLOY_USER" &>/dev/null; then
        # 终止用户所有进程
        pkill -u "$DEPLOY_USER" 2>/dev/null || true
        sleep 2
        
        # 强制终止剩余进程
        pkill -9 -u "$DEPLOY_USER" 2>/dev/null || true
        
        log_info "已终止用户 $DEPLOY_USER 的所有进程"
    fi
}

# 删除用户和文件
delete_user_and_files() {
    log_step "删除用户和文件"
    
    # 删除用户主目录
    if [[ -d "$USER_HOME" ]]; then
        rm -rf "$USER_HOME"
        log_info "已删除用户主目录: $USER_HOME"
    fi
    
    # 删除用户账户
    if id "$DEPLOY_USER" &>/dev/null; then
        userdel "$DEPLOY_USER" 2>/dev/null || true
        log_info "已删除用户账户: $DEPLOY_USER"
    fi
    
    # 删除sudo权限文件
    if [[ -f "/etc/sudoers.d/$DEPLOY_USER" ]]; then
        rm -f "/etc/sudoers.d/$DEPLOY_USER"
        log_info "已删除sudo权限配置"
    fi
}

# 清理项目目录
cleanup_project_directories() {
    log_step "清理项目目录"
    
    # 删除可能的项目目录
    local project_dirs=(
        "/opt/monitor-backend"
        "/var/log/monitor-backend"
        "/tmp/monitor-backend"
    )
    
    for dir in "${project_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            rm -rf "$dir"
            log_info "已删除目录: $dir"
        fi
    done
}

# 清理日志文件
cleanup_logs() {
    log_step "清理日志文件"
    
    # 清理systemd日志
    journalctl --vacuum-time=1s --unit="$SERVICE_NAME" 2>/dev/null || true
    
    # 清理可能的日志文件
    local log_files=(
        "/var/log/monitor_backend.log"
        "/var/log/monitor-backend.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            rm -f "$log_file"
            log_info "已删除日志文件: $log_file"
        fi
    done
}

# 验证清理结果
verify_cleanup() {
    log_step "验证清理结果"
    
    local cleanup_success=true
    
    # 检查用户是否删除
    if id "$DEPLOY_USER" &>/dev/null; then
        log_error "用户 $DEPLOY_USER 仍然存在"
        cleanup_success=false
    fi
    
    # 检查主目录是否删除
    if [[ -d "$USER_HOME" ]]; then
        log_error "用户主目录仍然存在: $USER_HOME"
        cleanup_success=false
    fi
    
    # 检查服务是否删除
    if systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
        log_error "系统服务仍然存在: $SERVICE_NAME"
        cleanup_success=false
    fi
    
    if $cleanup_success; then
        log_info "清理验证通过，所有组件已完全删除"
    else
        log_warn "清理可能不完整，请手动检查剩余组件"
    fi
}

# 显示清理结果
show_cleanup_result() {
    echo ""
    echo "=========================================="
    echo "监控后台用户删除完成"
    echo "=========================================="
    echo ""
    echo "已删除的组件:"
    echo "  [完成] 用户账户: $DEPLOY_USER"
    echo "  [完成] 用户主目录: $USER_HOME"
    echo "  [完成] 系统服务: $SERVICE_NAME"
    echo "  [完成] Nginx配置"
    echo "  [完成] 防火墙规则"
    echo "  [完成] 项目文件"
    echo "  [完成] 日志文件"
    echo ""
    echo "系统已恢复到安装前状态"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "星巴克风控绕过系统 - 监控后台用户删除"
    echo "=========================================="
    
    check_permissions
    confirm_deletion
    stop_services
    remove_nginx_config
    close_firewall_ports
    kill_user_processes
    delete_user_and_files
    cleanup_project_directories
    cleanup_logs
    verify_cleanup
    show_cleanup_result
    
    log_info "监控后台用户删除完成！"
}

# 执行主函数
main "$@"
