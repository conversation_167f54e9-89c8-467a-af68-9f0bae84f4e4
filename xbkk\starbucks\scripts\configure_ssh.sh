#!/bin/bash

# SSH端口配置脚本
# 作者: YINGAshadow
# 用途: 配置SSH端口为28262

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 备份SSH配置
backup_ssh_config() {
    log_step "备份SSH配置文件"
    
    if [[ ! -f /etc/ssh/sshd_config.backup ]]; then
        cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
        log_info "SSH配置已备份到 /etc/ssh/sshd_config.backup"
    else
        log_info "SSH配置备份已存在"
    fi
}

# 配置SSH端口
configure_ssh_port() {
    log_step "配置SSH端口为28262"
    
    # 检查当前端口配置
    current_port=$(grep "^Port " /etc/ssh/sshd_config | awk '{print $2}' || echo "22")
    log_info "当前SSH端口: $current_port"
    
    # 修改端口配置
    if grep -q "^Port " /etc/ssh/sshd_config; then
        sed -i 's/^Port .*/Port 28262/' /etc/ssh/sshd_config
    else
        echo "Port 28262" >> /etc/ssh/sshd_config
    fi
    
    # 确保其他安全配置
    if ! grep -q "^PermitRootLogin" /etc/ssh/sshd_config; then
        echo "PermitRootLogin yes" >> /etc/ssh/sshd_config
    fi
    
    if ! grep -q "^PasswordAuthentication" /etc/ssh/sshd_config; then
        echo "PasswordAuthentication yes" >> /etc/ssh/sshd_config
    fi
    
    log_info "SSH端口已配置为28262"
}

# 配置防火墙
configure_firewall_ssh() {
    log_step "配置防火墙开放SSH端口28262"
    
    # 开放新端口
    ufw allow 28262/tcp comment "SSH端口28262"
    
    # 显示防火墙状态
    ufw status numbered
    
    log_info "防火墙已开放端口28262"
}

# 测试SSH配置
test_ssh_config() {
    log_step "测试SSH配置"
    
    if sshd -t; then
        log_info "SSH配置测试通过"
    else
        log_error "SSH配置测试失败"
        exit 1
    fi
}

# 重启SSH服务
restart_ssh_service() {
    log_step "重启SSH服务"
    
    systemctl restart sshd
    systemctl enable sshd
    
    if systemctl is-active --quiet sshd; then
        log_info "SSH服务重启成功"
    else
        log_error "SSH服务重启失败"
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    log_step "SSH配置完成"
    
    echo ""
    echo "=========================================="
    echo "SSH端口配置完成"
    echo "=========================================="
    echo ""
    echo "新的SSH连接信息:"
    echo "  端口: 28262"
    echo "  连接命令: ssh -p 28262 用户名@服务器IP"
    echo ""
    echo "重要提醒:"
    echo "  1. 请确保防火墙已开放端口28262"
    echo "  2. 请在新终端测试SSH连接"
    echo "  3. 确认连接正常后再关闭当前会话"
    echo "  4. 如有问题，可使用备份文件恢复:"
    echo "     cp /etc/ssh/sshd_config.backup /etc/ssh/sshd_config"
    echo "     systemctl restart sshd"
    echo ""
}

# 主函数
main() {
    log_info "开始配置SSH端口"
    
    check_root
    backup_ssh_config
    configure_ssh_port
    configure_firewall_ssh
    test_ssh_config
    restart_ssh_service
    show_connection_info
    
    log_info "SSH端口配置完成！"
}

# 执行主函数
main "$@"
