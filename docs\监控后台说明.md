# 星巴克风控绕过系统 - 独立监控后台

**专用于监控所有客户接口使用情况，防止客户装后门捣乱**

## 系统概述

独立的监控后台系统，与主系统分离部署，专门用于：

- 监控所有客户API调用
- 记录详细的访问日志
- 检测异常行为和后门
- 提供企业级Web管理界面

## 核心功能

### 1. 全面监控
- 记录所有API请求和响应
- 监控客户行为模式
- 检测异常访问和后门行为
- 实时告警机制

### 2. 日志管理
- 详细的访问日志记录
- 请求参数和响应数据
- 客户身份识别
- 时间戳和IP追踪

### 3. 安全防护
- 自动检测后门行为
- 异常流量识别
- 恶意请求拦截
- 客户权限管理

### 4. 企业级Web界面
- 现代化管理后台
- 实时数据可视化
- 图表和统计分析
- 响应式设计

## 快速部署

```bash
# 1. 部署监控后台
cd monitor_backend
chmod +x deploy_monitor.sh
sudo ./deploy_monitor.sh

# 2. 启动监控服务
sudo systemctl start monitor-backend
sudo systemctl enable monitor-backend

# 3. 验证部署
curl http://localhost:9000/health
```

## 监控配置

监控后台运行在独立端口 9000，与主系统完全分离。

### 环境变量

```bash
# 监控后台配置
MONITOR_HOST=0.0.0.0
MONITOR_PORT=9000
MONITOR_SECRET=your-monitor-secret-key

# 主系统连接
MAIN_SYSTEM_URL=http://localhost:8000
MAIN_SYSTEM_SECRET=your-main-system-secret

# 数据库配置
MONITOR_DB_URL=sqlite:///./monitor_logs.db

# Web界面配置
WEB_SECRET_KEY=your-web-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123456
```

## Web管理界面

### 访问方式
- 访问地址: http://服务器IP:9000
- 登录账户: admin
- 默认密码: admin123456

### 主要功能
- 实时监控仪表板
- API调用统计
- 客户行为分析
- 安全事件告警
- 系统性能监控

### 界面特性
- Bootstrap响应式设计
- Chart.js数据可视化
- WebSocket实时更新
- JWT安全认证

## API接口

### 查看监控数据

```bash
# 查看实时日志
curl -H "Authorization: Bearer your-token" \
  http://localhost:9000/api/logs/realtime

# 查看客户统计
curl -H "Authorization: Bearer your-token" \
  http://localhost:9000/api/stats/customers

# 查看异常报告
curl -H "Authorization: Bearer your-token" \
  http://localhost:9000/api/alerts/anomalies
```

### 系统管理

```bash
# 系统健康检查
curl http://localhost:9000/health

# 获取系统状态
curl -H "Authorization: Bearer your-token" \
  http://localhost:9000/api/system/status

# 获取监控统计
curl -H "Authorization: Bearer your-token" \
  http://localhost:9000/api/monitor/stats
```

## 安全特性

### 数据加密
- 所有日志数据加密存储
- 传输过程TLS加密
- 敏感信息脱敏处理

### 访问控制
- 强身份认证
- JWT令牌验证
- 权限分级管理
- IP白名单限制

### 审计追踪
- 完整的操作审计
- 数据访问记录
- 系统变更日志

## 监控指标

### 系统指标
- API调用次数
- 响应时间统计
- 错误率监控
- 系统资源使用

### 安全指标
- 异常访问检测
- 后门行为识别
- 恶意请求统计
- 客户行为分析

### 业务指标
- 客户使用统计
- 功能使用分析
- 性能指标监控
- 收入相关数据

## 告警机制

### 实时告警
- 异常行为立即告警
- 系统故障自动通知
- 安全事件实时推送

### 告警渠道
- Web界面通知
- 系统日志记录
- API接口推送

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐
│   主系统        │    │   监控后台      │
│  (端口8000)     │◄──►│  (端口9000)     │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       │
┌─────────────────┐    ┌─────────────────┐
│   客户请求      │    │   监控数据库    │
│   (端口8094)    │    │  (独立存储)     │
└─────────────────┘    └─────────────────┘
```

## 技术栈

### 后端技术
- FastAPI Web框架
- SQLite数据库
- JWT认证
- 异步处理

### 前端技术
- Bootstrap 5 UI框架
- Chart.js 数据可视化
- WebSocket 实时通信
- Jinja2 模板引擎

### 部署技术
- systemd 服务管理
- Nginx 反向代理
- UFW 防火墙
- 非root用户运行

## 维护管理

### 服务管理
```bash
# 启动服务
sudo systemctl start monitor-backend

# 停止服务
sudo systemctl stop monitor-backend

# 重启服务
sudo systemctl restart monitor-backend

# 查看状态
sudo systemctl status monitor-backend
```

### 日志查看
```bash
# 查看服务日志
sudo journalctl -u monitor-backend -f

# 查看应用日志
sudo tail -f /opt/monitor_backend/logs/monitor.log
```

### 数据备份
```bash
# 备份数据库
sudo cp /opt/monitor_backend/data/monitor_logs.db /backup/

# 备份配置文件
sudo cp /opt/monitor_backend/.env /backup/
```

## 技术支持

### 监控后台管理
- 7x24小时监控
- 实时数据分析
- 异常行为检测
- 自动化响应

### 数据安全保障
- 加密存储
- 安全传输
- 访问控制
- 审计追踪

---

**版本**: v1.0.0  
**更新时间**: 2025-07-31  
**用途**: 独立监控后台，防止客户装后门捣乱
