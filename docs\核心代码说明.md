# 星巴克F5 Shape风控绕过系统 - 核心代码说明

## 核心代码结构

本目录包含星巴克F5 Shape风控绕过系统的核心代码实现。

### 快速启动

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
# 编辑 .env 文件配置必要参数

# 启动系统
python run.py
```

### 核心模块

- **src/core/f5_shape_generator.py**: F5 Shape指纹生成器
- **src/core/device_manager.py**: 设备管理器  
- **src/core/bypass_analyzer.py**: 风控绕过分析器
- **src/api/main.py**: FastAPI主应用
- **src/config/settings.py**: 配置管理

### 目录结构

```
starbucks/
├── src/                        # 源代码目录
│   ├── api/                    # API模块
│   ├── core/                   # 核心功能模块
│   ├── config/                 # 配置模块
│   └── utils/                  # 工具模块
├── scripts/                    # 部署和管理脚本
├── tests/                      # 测试代码
├── logs/                       # 日志文件
├── requirements.txt            # Python依赖
├── run.py                      # 系统启动脚本
├── .env                        # 环境配置
└── starbucks_devices.db        # SQLite数据库
```

## 重要说明

**完整的项目文档、使用说明、部署指南等请查看项目根目录的 docs/ 文件夹：**

- **docs/README.md** - 完整系统文档
- **docs/给客户的接口说明.md** - 客户使用指南
- **docs/安全部署流程说明.md** - 部署说明

## 测试验证

```bash
# API接口测试
./scripts/test_api.sh

# 运行单元测试
./scripts/run_tests.sh

# 验证部署
./scripts/verify_deployment.sh
```

## 技术支持

如有问题请查看项目根目录 docs/ 文件夹下的完整文档。
