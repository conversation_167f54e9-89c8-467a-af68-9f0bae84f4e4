#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
F5 Shape指纹数据分析器
作者：YINGAshadow
创建时间：2025-7-29
功能：深度分析abcd.txt中的F5 Shape指纹数据结构，逆向真实算法
"""

import json
import base64
import hashlib
import struct
import binascii
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import re
import zlib
from collections import Counter


class F5ShapeAnalyzer:
    """F5 Shape指纹数据分析器"""
    
    def __init__(self, data_file: str = "abcd.txt"):
        """
        初始化分析器
        
        Args:
            data_file: 指纹数据文件路径
        """
        self.data_file = data_file
        self.fingerprint_samples = []
        self.analysis_results = {}
        self.patterns = {}
        
        # 加载数据
        self._load_fingerprint_data()
        
    def _load_fingerprint_data(self):
        """加载指纹数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            data = json.loads(line)
                            self.fingerprint_samples.append(data)
                        except json.JSONDecodeError:
                            continue
            
            print(f"成功加载 {len(self.fingerprint_samples)} 个指纹样本")
            
        except FileNotFoundError:
            print(f"错误：找不到数据文件 {self.data_file}")
            raise
    
    def analyze_all_fields(self) -> Dict[str, Any]:
        """分析所有指纹字段"""
        print("开始深度分析F5 Shape指纹数据...")
        
        # 分析各个字段
        self.analysis_results = {
            "device_id": self._analyze_device_id(),
            "x_xhpacpxq_z": self._analyze_z_field(),
            "x_xhpacpxq_g": self._analyze_g_field(),
            "x_xhpacpxq_e": self._analyze_e_field(),
            "x_xhpacpxq_f": self._analyze_f_field(),
            "x_xhpacpxq_d": self._analyze_d_field(),
            "x_xhpacpxq_c": self._analyze_c_field(),
            "x_xhpacpxq_b": self._analyze_b_field(),
            "x_xhpacpxq_a": self._analyze_a_field(),
            "authorization": self._analyze_authorization(),
            "x_bs_device_id": self._analyze_bs_device_id(),
            "time": self._analyze_time_field(),
            "correlations": self._analyze_field_correlations()
        }
        
        return self.analysis_results
    
    def _analyze_device_id(self) -> Dict[str, Any]:
        """分析x-device-id字段"""
        device_ids = [sample.get("x-device-id", "") for sample in self.fingerprint_samples]
        
        # 检查唯一性
        unique_ids = set(device_ids)
        
        # 分析格式
        format_analysis = {}
        if device_ids:
            sample_id = device_ids[0]
            format_analysis = {
                "length": len(sample_id),
                "format": "UUID" if "-" in sample_id else "其他",
                "pattern": self._extract_pattern(sample_id),
                "is_static": len(unique_ids) == 1
            }
        
        return {
            "total_samples": len(device_ids),
            "unique_count": len(unique_ids),
            "format_analysis": format_analysis,
            "sample_values": list(unique_ids)[:5]  # 前5个样本
        }
    
    def _analyze_z_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-z字段"""
        z_values = [sample.get("X-XHPAcPXq-z", "") for sample in self.fingerprint_samples]
        unique_values = set(z_values)
        
        return {
            "total_samples": len(z_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1,
            "values": list(unique_values),
            "analysis": "固定值字段，用于标识F5 Shape版本或类型"
        }
    
    def _analyze_g_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-g字段（主要指纹数据）"""
        g_values = [sample.get("X-XHPAcPXq-g", "") for sample in self.fingerprint_samples]
        
        analysis = {
            "total_samples": len(g_values),
            "unique_count": len(set(g_values)),
            "is_dynamic": len(set(g_values)) > 1,
            "structure_analysis": self._analyze_g_structure(g_values),
            "base64_analysis": self._analyze_base64_content(g_values),
            "pattern_analysis": self._find_g_patterns(g_values)
        }
        
        return analysis
    
    def _analyze_g_structure(self, g_values: List[str]) -> Dict[str, Any]:
        """分析G字段的结构"""
        if not g_values:
            return {}
        
        # 分析分号分隔的结构
        structure_counts = Counter()
        segment_analysis = {}
        
        for g_value in g_values[:10]:  # 分析前10个样本
            if ";" in g_value:
                segments = g_value.split(";")
                structure_counts[len(segments)] += 1
                
                for i, segment in enumerate(segments):
                    if f"segment_{i}" not in segment_analysis:
                        segment_analysis[f"segment_{i}"] = {
                            "lengths": [],
                            "patterns": [],
                            "base64_valid": []
                        }
                    
                    segment_analysis[f"segment_{i}"]["lengths"].append(len(segment))
                    segment_analysis[f"segment_{i}"]["patterns"].append(self._extract_pattern(segment))
                    segment_analysis[f"segment_{i}"]["base64_valid"].append(self._is_valid_base64(segment))
        
        return {
            "structure_counts": dict(structure_counts),
            "segment_analysis": segment_analysis,
            "common_structure": structure_counts.most_common(1)[0] if structure_counts else None
        }
    
    def _analyze_e_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-e字段（动态指纹数据）"""
        e_values = [sample.get("X-XHPAcPXq-e", "") for sample in self.fingerprint_samples]
        
        return {
            "total_samples": len(e_values),
            "unique_count": len(set(e_values)),
            "is_dynamic": len(set(e_values)) > 1,
            "structure_analysis": self._analyze_e_structure(e_values),
            "time_correlation": self._analyze_e_time_correlation_with_values(e_values),
            "pattern_analysis": self._find_e_patterns(e_values)
        }
    
    def _analyze_e_structure(self, e_values: List[str]) -> Dict[str, Any]:
        """分析E字段的结构"""
        if not e_values:
            return {}
        
        # E字段通常以"b;"开头
        prefix_analysis = Counter()
        segment_analysis = {}
        
        for e_value in e_values[:10]:
            if ";" in e_value:
                segments = e_value.split(";")
                prefix = segments[0] if segments else ""
                prefix_analysis[prefix] += 1
                
                # 分析主要数据段
                if len(segments) >= 2:
                    main_segment = segments[1]
                    if "main_segment" not in segment_analysis:
                        segment_analysis["main_segment"] = {
                            "lengths": [],
                            "base64_valid": [],
                            "patterns": []
                        }
                    
                    segment_analysis["main_segment"]["lengths"].append(len(main_segment))
                    segment_analysis["main_segment"]["base64_valid"].append(self._is_valid_base64(main_segment))
                    segment_analysis["main_segment"]["patterns"].append(self._extract_pattern(main_segment))
        
        return {
            "prefix_analysis": dict(prefix_analysis),
            "segment_analysis": segment_analysis
        }
    
    def _analyze_f_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-f字段"""
        f_values = [sample.get("X-XHPAcPXq-f", "") for sample in self.fingerprint_samples]
        unique_values = set(f_values)
        
        analysis = {
            "total_samples": len(f_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1,
            "base64_analysis": self._analyze_base64_content(f_values[:1]) if f_values else {}
        }
        
        if len(unique_values) == 1 and f_values:
            # 如果是固定值，尝试解码分析
            fixed_value = f_values[0]
            analysis["fixed_value_analysis"] = self._analyze_fixed_base64(fixed_value)
        
        return analysis
    
    def _analyze_d_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-d字段"""
        d_values = [sample.get("X-XHPAcPXq-d", "") for sample in self.fingerprint_samples]
        unique_values = set(d_values)
        
        analysis = {
            "total_samples": len(d_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1
        }
        
        if len(unique_values) == 1 and d_values:
            # D字段通常包含设备特征信息
            fixed_value = d_values[0]
            analysis["device_features_analysis"] = self._analyze_device_features(fixed_value)
        
        return analysis
    
    def _analyze_c_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-c字段"""
        c_values = [sample.get("X-XHPAcPXq-c", "") for sample in self.fingerprint_samples]
        unique_values = set(c_values)
        
        return {
            "total_samples": len(c_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1,
            "base64_analysis": self._analyze_base64_content(c_values[:1]) if c_values else {}
        }
    
    def _analyze_b_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-b字段"""
        b_values = [sample.get("X-XHPAcPXq-b", "") for sample in self.fingerprint_samples]
        unique_values = set(b_values)
        
        return {
            "total_samples": len(b_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1,
            "values": list(unique_values),
            "analysis": "短标识符，可能是版本或配置标识"
        }
    
    def _analyze_a_field(self) -> Dict[str, Any]:
        """分析X-XHPAcPXq-a字段（最复杂的指纹数据）"""
        a_values = [sample.get("X-XHPAcPXq-a", "") for sample in self.fingerprint_samples]
        
        return {
            "total_samples": len(a_values),
            "unique_count": len(set(a_values)),
            "is_dynamic": len(set(a_values)) > 1,
            "structure_analysis": self._analyze_a_structure(a_values),
            "complexity_analysis": self._analyze_a_complexity(a_values)
        }
    
    def _analyze_authorization(self) -> Dict[str, Any]:
        """分析Authorization字段"""
        auth_values = [sample.get("Authorization", "") for sample in self.fingerprint_samples]
        unique_values = set(auth_values)
        
        analysis = {
            "total_samples": len(auth_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1
        }
        
        if auth_values:
            sample_auth = auth_values[0]
            analysis["format_analysis"] = {
                "length": len(sample_auth),
                "is_hex": all(c in "0123456789abcdef" for c in sample_auth.lower()),
                "is_hash": len(sample_auth) in [32, 40, 64]  # MD5, SHA1, SHA256长度
            }
        
        return analysis
    
    def _analyze_bs_device_id(self) -> Dict[str, Any]:
        """分析x-bs-device-id字段"""
        bs_values = [sample.get("x-bs-device-id", "") for sample in self.fingerprint_samples]
        unique_values = set(bs_values)
        
        return {
            "total_samples": len(bs_values),
            "unique_count": len(unique_values),
            "is_static": len(unique_values) == 1,
            "length_analysis": [len(v) for v in bs_values[:5]] if bs_values else []
        }
    
    def _analyze_time_field(self) -> Dict[str, Any]:
        """分析time字段"""
        time_values = [sample.get("time", "") for sample in self.fingerprint_samples]
        
        # 解析时间格式
        time_objects = []
        for time_str in time_values:
            try:
                time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                time_objects.append(time_obj)
            except ValueError:
                continue
        
        analysis = {
            "total_samples": len(time_values),
            "valid_timestamps": len(time_objects),
            "format": "YYYY-MM-DD HH:MM:SS"
        }
        
        if len(time_objects) >= 2:
            # 分析时间间隔
            intervals = []
            for i in range(1, len(time_objects)):
                interval = (time_objects[i] - time_objects[i-1]).total_seconds()
                intervals.append(interval)
            
            analysis["time_intervals"] = {
                "min_interval": min(intervals),
                "max_interval": max(intervals),
                "avg_interval": sum(intervals) / len(intervals)
            }
        
        return analysis
    
    def _analyze_field_correlations(self) -> Dict[str, Any]:
        """分析字段间的关联关系"""
        correlations = {}
        
        # 分析E字段和时间的关联
        e_time_correlation = self._analyze_e_time_correlation()
        if e_time_correlation:
            correlations["e_time"] = e_time_correlation

        # 分析G字段和E字段的关联
        g_e_correlation = self._analyze_g_e_correlation()
        if g_e_correlation:
            correlations["g_e"] = g_e_correlation
        
        return correlations
    
    def _extract_pattern(self, value: str) -> str:
        """提取字符串模式"""
        if not value:
            return ""
        
        pattern = ""
        for char in value:
            if char.isalpha():
                pattern += "A"
            elif char.isdigit():
                pattern += "9"
            elif char in "+-_=":
                pattern += char
            else:
                pattern += "X"
        
        return pattern
    
    def _is_valid_base64(self, value: str) -> bool:
        """检查是否为有效的Base64编码"""
        try:
            base64.b64decode(value + "==")  # 添加padding
            return True
        except Exception:
            return False
    
    def _analyze_base64_content(self, values: List[str]) -> Dict[str, Any]:
        """分析Base64编码内容"""
        if not values:
            return {}
        
        analysis = {}
        for i, value in enumerate(values[:3]):  # 分析前3个
            try:
                decoded = base64.b64decode(value + "==")
                analysis[f"sample_{i}"] = {
                    "original_length": len(value),
                    "decoded_length": len(decoded),
                    "decoded_hex": decoded[:32].hex() if len(decoded) >= 32 else decoded.hex(),
                    "is_binary": not all(32 <= b <= 126 for b in decoded[:100])
                }
            except Exception as e:
                analysis[f"sample_{i}"] = {"error": str(e)}
        
        return analysis
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        if not self.analysis_results:
            print("请先运行 analyze_all_fields() 方法")
            return
        
        print("\n" + "="*60)
        print("F5 Shape指纹数据分析报告")
        print("="*60)
        
        for field_name, analysis in self.analysis_results.items():
            if field_name == "correlations":
                continue
                
            print(f"\n【{field_name.upper()}字段分析】")
            print(f"样本数量: {analysis.get('total_samples', 0)}")
            print(f"唯一值数量: {analysis.get('unique_count', 0)}")
            
            if analysis.get('is_static'):
                print("类型: 静态字段（固定值）")
            elif analysis.get('is_dynamic'):
                print("类型: 动态字段（变化值）")
            
            # 显示特殊分析结果
            if 'structure_analysis' in analysis:
                print("结构分析: 复杂多段结构")
            if 'base64_analysis' in analysis:
                print("编码: Base64编码")
        
        print(f"\n【关联分析】")
        correlations = self.analysis_results.get('correlations', {})
        if correlations:
            for corr_name, corr_data in correlations.items():
                print(f"{corr_name}: {corr_data}")
        else:
            print("未发现明显的字段关联关系")
        
        print("\n" + "="*60)


    def _analyze_e_time_correlation_with_values(self, e_values: List[str]) -> Optional[Dict[str, Any]]:
        """分析E字段与时间的关联（带参数版本）"""
        if len(self.fingerprint_samples) < 2:
            return None

        correlations = []
        time_e_pairs = []

        # 使用传入的e_values和样本中的时间
        for i, sample in enumerate(self.fingerprint_samples):
            if i < len(e_values):
                time_str = sample.get("time", "")
                e_value = e_values[i]

                if time_str and e_value:
                    try:
                        time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                        timestamp = int(time_obj.timestamp())
                        time_e_pairs.append((timestamp, e_value))
                    except ValueError:
                        continue

        if len(time_e_pairs) < 2:
            return None

        # 分析E字段中的时间戳编码
        for timestamp, e_value in time_e_pairs:
            if ";" in e_value:
                segments = e_value.split(";")
                if len(segments) >= 2:
                    main_segment = segments[1]
                    # 尝试在E字段中找到时间戳的编码形式
                    timestamp_hex = hex(timestamp)[2:]
                    timestamp_b64 = base64.b64encode(str(timestamp).encode()).decode()

                    correlations.append({
                        "timestamp": timestamp,
                        "e_segment": main_segment,
                        "timestamp_in_hex": timestamp_hex in main_segment,
                        "timestamp_in_b64": timestamp_b64 in main_segment,
                        "segment_length": len(main_segment)
                    })

        return {
            "total_pairs": len(time_e_pairs),
            "correlations": correlations[:5],  # 前5个样本
            "analysis": "E字段主段可能包含时间戳的编码信息"
        }

    def _analyze_e_time_correlation(self) -> Optional[Dict[str, Any]]:
        """分析E字段与时间的关联（无参数版本）"""
        if len(self.fingerprint_samples) < 2:
            return None

        correlations = []
        time_e_pairs = []

        for sample in self.fingerprint_samples:
            time_str = sample.get("time", "")
            e_value = sample.get("X-XHPAcPXq-e", "")

            if time_str and e_value:
                try:
                    time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                    timestamp = int(time_obj.timestamp())
                    time_e_pairs.append((timestamp, e_value))
                except ValueError:
                    continue

        if len(time_e_pairs) < 2:
            return None

        # 分析E字段中的时间戳编码
        for timestamp, e_value in time_e_pairs:
            if ";" in e_value:
                segments = e_value.split(";")
                if len(segments) >= 2:
                    main_segment = segments[1]
                    # 尝试在E字段中找到时间戳的编码形式
                    timestamp_hex = hex(timestamp)[2:]
                    timestamp_b64 = base64.b64encode(str(timestamp).encode()).decode()

                    correlations.append({
                        "timestamp": timestamp,
                        "e_segment": main_segment,
                        "timestamp_in_hex": timestamp_hex in main_segment,
                        "timestamp_in_b64": timestamp_b64 in main_segment,
                        "segment_length": len(main_segment)
                    })

        return {
            "total_pairs": len(time_e_pairs),
            "correlations": correlations[:5],  # 前5个样本
            "analysis": "E字段主段可能包含时间戳的编码信息"
        }

    def _analyze_g_e_correlation(self) -> Optional[Dict[str, Any]]:
        """分析G字段与E字段的关联"""
        if len(self.fingerprint_samples) < 2:
            return None

        g_e_pairs = []
        correlations = []

        for sample in self.fingerprint_samples:
            g_value = sample.get("X-XHPAcPXq-g", "")
            e_value = sample.get("X-XHPAcPXq-e", "")

            if g_value and e_value:
                g_e_pairs.append((g_value, e_value))

        if len(g_e_pairs) < 2:
            return None

        # 分析G和E字段的结构关联
        for g_val, e_val in g_e_pairs[:5]:
            g_segments = g_val.split(";") if ";" in g_val else [g_val]
            e_segments = e_val.split(";") if ";" in e_val else [e_val]

            # 检查是否有共同的编码片段
            common_patterns = []
            for g_seg in g_segments:
                for e_seg in e_segments:
                    if len(g_seg) > 10 and len(e_seg) > 10:
                        # 检查Base64编码的相似性
                        if self._calculate_similarity(g_seg, e_seg) > 0.3:
                            common_patterns.append({
                                "g_segment": g_seg[:20] + "...",
                                "e_segment": e_seg[:20] + "...",
                                "similarity": self._calculate_similarity(g_seg, e_seg)
                            })

            correlations.append({
                "g_segments_count": len(g_segments),
                "e_segments_count": len(e_segments),
                "common_patterns": common_patterns
            })

        return {
            "total_pairs": len(g_e_pairs),
            "correlations": correlations,
            "analysis": "G和E字段可能共享某些编码组件"
        }

    def _find_g_patterns(self, g_values: List[str]) -> Dict[str, Any]:
        """查找G字段的模式"""
        if not g_values:
            return {}

        patterns = {
            "segment_patterns": {},
            "length_patterns": {},
            "base64_patterns": {},
            "time_patterns": {}
        }

        for g_value in g_values[:10]:
            if ";" in g_value:
                segments = g_value.split(";")

                # 分析每个段的模式
                for i, segment in enumerate(segments):
                    segment_key = f"segment_{i}"

                    if segment_key not in patterns["segment_patterns"]:
                        patterns["segment_patterns"][segment_key] = {
                            "lengths": [],
                            "is_base64": [],
                            "patterns": []
                        }

                    patterns["segment_patterns"][segment_key]["lengths"].append(len(segment))
                    patterns["segment_patterns"][segment_key]["is_base64"].append(self._is_valid_base64(segment))
                    patterns["segment_patterns"][segment_key]["patterns"].append(self._extract_pattern(segment))

                # 分析长度模式
                total_length = len(g_value)
                length_category = "short" if total_length < 100 else "medium" if total_length < 500 else "long"
                patterns["length_patterns"][length_category] = patterns["length_patterns"].get(length_category, 0) + 1

        return patterns

    def _find_e_patterns(self, e_values: List[str]) -> Dict[str, Any]:
        """查找E字段的模式"""
        if not e_values:
            return {}

        patterns = {
            "prefix_patterns": {},
            "main_segment_patterns": {},
            "time_encoding_patterns": {}
        }

        for e_value in e_values[:10]:
            if ";" in e_value:
                segments = e_value.split(";")

                # 分析前缀模式
                if segments:
                    prefix = segments[0]
                    patterns["prefix_patterns"][prefix] = patterns["prefix_patterns"].get(prefix, 0) + 1

                # 分析主段模式
                if len(segments) >= 2:
                    main_segment = segments[1]

                    # 检查是否包含时间戳编码
                    current_time = int(datetime.now().timestamp())
                    time_variants = [
                        str(current_time),
                        str(current_time)[:8],  # 截断时间戳
                        hex(current_time)[2:],  # 十六进制
                    ]

                    time_found = any(variant in main_segment for variant in time_variants)
                    patterns["time_encoding_patterns"]["contains_timestamp"] = time_found

                    # 分析主段长度分布
                    length_key = f"length_{len(main_segment)//10*10}-{len(main_segment)//10*10+9}"
                    patterns["main_segment_patterns"][length_key] = patterns["main_segment_patterns"].get(length_key, 0) + 1

        return patterns

    def _analyze_fixed_base64(self, value: str) -> Dict[str, Any]:
        """分析固定Base64值"""
        if not value:
            return {}

        analysis = {
            "original_value": value,
            "length": len(value),
            "decoded_analysis": {},
            "structure_analysis": {},
            "pattern_analysis": {}
        }

        try:
            # 尝试Base64解码
            decoded = base64.b64decode(value + "==")
            analysis["decoded_analysis"] = {
                "decoded_length": len(decoded),
                "decoded_hex": decoded.hex(),
                "is_binary": not all(32 <= b <= 126 for b in decoded),
                "contains_nulls": b'\x00' in decoded,
                "entropy": self._calculate_entropy(decoded)
            }

            # 分析解码后的结构
            if len(decoded) >= 4:
                # 检查是否包含长度信息
                possible_length = struct.unpack('>I', decoded[:4])[0]
                analysis["structure_analysis"]["possible_length_header"] = possible_length

                # 检查是否包含时间戳
                for i in range(0, len(decoded) - 4, 4):
                    timestamp_candidate = struct.unpack('>I', decoded[i:i+4])[0]
                    if 1600000000 < timestamp_candidate < 2000000000:  # 合理的时间戳范围
                        analysis["structure_analysis"]["possible_timestamp"] = {
                            "position": i,
                            "value": timestamp_candidate,
                            "datetime": datetime.fromtimestamp(timestamp_candidate).strftime("%Y-%m-%d %H:%M:%S")
                        }
                        break

        except Exception as e:
            analysis["decoded_analysis"]["error"] = str(e)

        # 分析Base64字符模式
        char_counts = {}
        for char in value:
            char_counts[char] = char_counts.get(char, 0) + 1

        analysis["pattern_analysis"] = {
            "char_distribution": dict(sorted(char_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            "padding_chars": value.count('='),
            "url_safe": all(c in "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=" for c in value)
        }

        return analysis

    def _analyze_device_features(self, value: str) -> Dict[str, Any]:
        """分析设备特征数据"""
        if not value:
            return {}

        analysis = {
            "original_value": value,
            "length": len(value),
            "device_info": {},
            "capability_flags": {},
            "encoded_features": {}
        }

        try:
            # 尝试Base64解码
            decoded = base64.b64decode(value + "==")

            # 分析设备特征位
            if len(decoded) >= 8:
                # 前8字节可能是设备能力标志
                capability_bytes = decoded[:8]
                analysis["capability_flags"] = {
                    "raw_bytes": capability_bytes.hex(),
                    "bit_analysis": self._analyze_capability_bits(capability_bytes)
                }

            # 查找可能的设备标识符
            if len(decoded) >= 16:
                # 检查是否包含设备ID或硬件信息
                for i in range(0, len(decoded) - 16, 4):
                    chunk = decoded[i:i+16]
                    if self._looks_like_device_id(chunk):
                        analysis["device_info"]["possible_device_id"] = {
                            "position": i,
                            "value": chunk.hex(),
                            "ascii_repr": ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
                        }
                        break

            # 分析屏幕分辨率等特征
            screen_patterns = self._find_screen_resolution_patterns(decoded)
            if screen_patterns:
                analysis["device_info"]["screen_features"] = screen_patterns

        except Exception as e:
            analysis["encoded_features"]["decode_error"] = str(e)

        return analysis

    def _analyze_a_structure(self, a_values: List[str]) -> Dict[str, Any]:
        """分析A字段结构"""
        if not a_values:
            return {}

        structure_analysis = {
            "complexity_metrics": {},
            "encoding_analysis": {},
            "pattern_analysis": {},
            "algorithm_hints": {}
        }

        for i, a_value in enumerate(a_values[:5]):
            sample_analysis = {
                "length": len(a_value),
                "segments": [],
                "encoding_layers": 0,
                "complexity_score": 0
            }

            # 分析多层编码结构
            current_value = a_value
            layer = 0

            while layer < 5:  # 最多分析5层
                if self._is_valid_base64(current_value):
                    try:
                        decoded = base64.b64decode(current_value + "==")
                        sample_analysis["encoding_layers"] += 1

                        # 检查解码后是否还是可打印字符串
                        if all(32 <= b <= 126 for b in decoded):
                            current_value = decoded.decode('utf-8')
                            layer += 1
                        else:
                            # 二进制数据，停止解码
                            sample_analysis["final_binary"] = decoded[:32].hex()
                            break
                    except:
                        break
                else:
                    break

            # 计算复杂度分数
            sample_analysis["complexity_score"] = self._calculate_complexity_score(a_value)

            structure_analysis["complexity_metrics"][f"sample_{i}"] = sample_analysis

        return structure_analysis

    def _analyze_a_complexity(self, a_values: List[str]) -> Dict[str, Any]:
        """分析A字段复杂度"""
        if not a_values:
            return {}

        complexity_analysis = {
            "entropy_analysis": {},
            "compression_analysis": {},
            "algorithm_detection": {},
            "security_features": {}
        }

        for i, a_value in enumerate(a_values[:3]):
            # 熵分析
            entropy = self._calculate_entropy(a_value.encode())

            # 压缩分析
            compressed = zlib.compress(a_value.encode())
            compression_ratio = len(compressed) / len(a_value.encode())

            # 算法检测
            algorithm_hints = self._detect_algorithm_patterns(a_value)

            complexity_analysis["entropy_analysis"][f"sample_{i}"] = {
                "entropy": entropy,
                "normalized_entropy": entropy / 8.0,  # 归一化到0-1
                "randomness_level": "high" if entropy > 6 else "medium" if entropy > 4 else "low"
            }

            complexity_analysis["compression_analysis"][f"sample_{i}"] = {
                "original_size": len(a_value),
                "compressed_size": len(compressed),
                "compression_ratio": compression_ratio,
                "compressibility": "low" if compression_ratio > 0.8 else "medium" if compression_ratio > 0.5 else "high"
            }

            complexity_analysis["algorithm_detection"][f"sample_{i}"] = algorithm_hints

        return complexity_analysis

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算两个字符串的相似度"""
        if not str1 or not str2:
            return 0.0

        # 使用简单的字符重叠度计算
        set1 = set(str1)
        set2 = set(str2)
        intersection = len(set1 & set2)
        union = len(set1 | set2)

        return intersection / union if union > 0 else 0.0

    def _calculate_entropy(self, data: bytes) -> float:
        """计算数据的熵值"""
        if not data:
            return 0.0

        # 计算字节频率
        byte_counts = {}
        for byte in data:
            byte_counts[byte] = byte_counts.get(byte, 0) + 1

        # 计算熵
        entropy = 0.0
        data_len = len(data)

        for count in byte_counts.values():
            probability = count / data_len
            if probability > 0:
                import math
                entropy -= probability * math.log2(probability)

        return entropy

    def _analyze_capability_bits(self, capability_bytes: bytes) -> Dict[str, Any]:
        """分析设备能力位"""
        analysis = {}

        for i, byte in enumerate(capability_bytes):
            bit_analysis = []
            for bit in range(8):
                if byte & (1 << bit):
                    bit_analysis.append(f"bit_{bit}")
            analysis[f"byte_{i}"] = {
                "value": byte,
                "hex": f"0x{byte:02x}",
                "set_bits": bit_analysis
            }

        return analysis

    def _looks_like_device_id(self, chunk: bytes) -> bool:
        """检查数据块是否像设备ID"""
        # 简单启发式：包含一定比例的可打印字符
        printable_count = sum(1 for b in chunk if 32 <= b <= 126)
        return printable_count / len(chunk) > 0.5

    def _find_screen_resolution_patterns(self, data: bytes) -> Optional[Dict[str, Any]]:
        """查找屏幕分辨率模式"""
        # 常见分辨率值
        common_resolutions = [
            (1920, 1080), (1366, 768), (1280, 720), (1440, 900),
            (1600, 900), (1024, 768), (1280, 1024), (1680, 1050)
        ]

        for width, height in common_resolutions:
            # 检查大端和小端编码
            width_bytes_be = struct.pack('>H', width)
            height_bytes_be = struct.pack('>H', height)
            width_bytes_le = struct.pack('<H', width)
            height_bytes_le = struct.pack('<H', height)

            if (width_bytes_be in data and height_bytes_be in data) or \
               (width_bytes_le in data and height_bytes_le in data):
                return {
                    "width": width,
                    "height": height,
                    "found_encoding": "big_endian" if width_bytes_be in data else "little_endian"
                }

        return None

    def _calculate_complexity_score(self, value: str) -> float:
        """计算字符串复杂度分数"""
        if not value:
            return 0.0

        score = 0.0

        # 长度分数
        score += min(len(value) / 100, 1.0) * 20

        # 字符多样性分数
        unique_chars = len(set(value))
        score += min(unique_chars / 64, 1.0) * 30

        # 熵分数
        entropy = self._calculate_entropy(value.encode())
        score += min(entropy / 8, 1.0) * 50

        return score

    def _detect_algorithm_patterns(self, value: str) -> Dict[str, Any]:
        """检测算法模式"""
        patterns = {
            "base64_layers": 0,
            "possible_hash": False,
            "encryption_hints": [],
            "compression_hints": []
        }

        # 检测Base64层数
        current = value
        while self._is_valid_base64(current) and patterns["base64_layers"] < 5:
            try:
                decoded = base64.b64decode(current + "==")
                if all(32 <= b <= 126 for b in decoded):
                    current = decoded.decode('utf-8')
                    patterns["base64_layers"] += 1
                else:
                    break
            except:
                break

        # 检测可能的哈希值
        if len(value) in [32, 40, 64, 128] and all(c in "0123456789abcdefABCDEF" for c in value):
            patterns["possible_hash"] = True

        # 检测加密特征
        if self._calculate_entropy(value.encode()) > 6:
            patterns["encryption_hints"].append("high_entropy")

        return patterns
