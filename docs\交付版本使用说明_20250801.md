# 交付版本使用说明

**目标**: 为客户准备纯净版本的星巴克风控绕过系统  
**操作**: 移除监控后台集成代码，保留本地系统监控  
**时间**: 2025-8-1  

## 核心区别

### 当前版本（开发版）
- ✅ **完整绕过功能**: F5 Shape绕过算法
- ✅ **本地系统监控**: system_monitor本地监控
- ✅ **监控后台集成**: 向monitor_backend发送客户数据
- ✅ **客户行为记录**: 记录所有客户API使用情况

### 交付版本（客户版）
- ✅ **完整绕过功能**: F5 Shape绕过算法（保留）
- ✅ **本地系统监控**: system_monitor本地监控（保留）
- ❌ **监控后台集成**: 移除向monitor_backend发送数据
- ❌ **客户行为记录**: 移除客户使用数据收集

---

## 需要移除的代码

### 1. 监控后台客户端
**文件**: `src/utils/monitor.py`
```python
# 需要移除的类
class MonitorBackendClient:
    # 整个类都要移除

# 需要移除的方法
async def record_customer_request(...):
    # 整个方法都要移除
```

### 2. API中间件集成
**文件**: `src/api/main.py`
```python
# 需要移除的代码段
# 获取客户ID（如果是客户API）
customer_id = "unknown"
if "/api/bypass/" in str(request.url.path):
    # ... 客户ID提取逻辑

# 读取响应体（用于监控）
response_body = ""
# ... 响应体读取逻辑

# 记录到监控后台（仅客户API）
if "/api/bypass/" in str(request.url.path):
    await system_monitor.metrics_collector.record_customer_request(...)
```

### 3. 环境配置
**文件**: `.env`
```bash
# 需要移除的配置
MONITOR_BACKEND_ENABLED=true
MONITOR_BACKEND_URL=http://localhost:9000
MONITOR_BACKEND_TOKEN=monitor_backend_secret_token_2025
```

---

## 保留的功能

### 1. 本地系统监控（保留）
```python
# 保留这些功能
system_monitor.get_status()
system_monitor.alert_manager.get_active_alerts()
await system_monitor.start_monitoring(interval=60)
await system_monitor.stop_monitoring()
```

### 2. 本地性能监控（保留）
```python
# 保留这些功能
system_monitor.metrics_collector.record_api_request(success, response_time)
system_monitor.metrics_collector.collect_system_metrics()
```

### 3. 本地日志记录（保留）
```python
# 保留这些功能
logger.info(f"API请求 - IP: {client_ip}, 路径: {request.url.path}")
```

---

## 使用脚本

### 1. 脚本位置
```
xbkk/starbucks/scripts/prepare_delivery_version.sh
```

### 2. 执行方法
```bash
# Linux环境
cd xbkk/starbucks
chmod +x scripts/prepare_delivery_version.sh
./scripts/prepare_delivery_version.sh

# Windows环境（WSL或Git Bash）
cd xbkk/starbucks
bash scripts/prepare_delivery_version.sh
```

### 3. 脚本功能
1. **自动备份**: 创建原始版本备份
2. **清理环境配置**: 移除MONITOR_BACKEND_*配置
3. **清理API代码**: 移除监控后台集成代码
4. **清理工具类**: 移除MonitorBackendClient类
5. **验证清理**: 确保监控后台代码完全移除
6. **生成文档**: 创建DELIVERY_VERSION.md说明

---

## 手动清理步骤

如果不使用脚本，可以手动执行以下步骤：

### 步骤1: 备份代码
```bash
cp -r xbkk/starbucks /tmp/starbucks_backup_$(date +%s)
```

### 步骤2: 清理.env文件
```bash
# 编辑 xbkk/starbucks/.env
# 删除以下行：
MONITOR_BACKEND_ENABLED=true
MONITOR_BACKEND_URL=http://localhost:9000
MONITOR_BACKEND_TOKEN=monitor_backend_secret_token_2025
```

### 步骤3: 清理main.py
编辑 `xbkk/starbucks/src/api/main.py`，移除：
- 客户ID获取逻辑
- 响应体读取代码（用于监控）
- record_customer_request调用
- 日志中的客户ID部分

### 步骤4: 清理monitor.py
编辑 `xbkk/starbucks/src/utils/monitor.py`，移除：
- MonitorBackendClient类（完整类定义）
- record_customer_request方法
- monitor_client初始化
- aiohttp导入（如果仅用于监控后台）

### 步骤5: 验证清理
```bash
# 检查是否还有监控后台相关代码
grep -r "MonitorBackendClient" xbkk/starbucks/src/
grep -r "MONITOR_BACKEND" xbkk/starbucks/
grep -r "record_customer_request" xbkk/starbucks/src/
```

---

## 验证交付版本

### 1. 功能测试
```bash
# 启动系统
cd xbkk/starbucks
python run.py

# 测试API
curl http://localhost:8888/health
curl -X POST http://localhost:8888/api/v1/fingerprints \
  -H "X-API-Key: test-api-key-2025" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 5}'
```

### 2. 监控功能验证
```bash
# 测试本地监控（应该正常）
curl http://localhost:8888/api/v1/system/status \
  -H "Authorization: Bearer 管理员Token"

# 确认无外部数据发送
# 检查日志，确保没有"发送监控日志失败"等错误
```

### 3. 清理验证
```bash
# 确认监控后台相关代码已移除
grep -r "MonitorBackendClient" xbkk/starbucks/src/ || echo "清理成功"
grep -r "record_customer_request" xbkk/starbucks/src/ || echo "清理成功"
```

---

## 交付检查清单

### ✅ 代码清理
- [ ] 移除MonitorBackendClient类
- [ ] 移除record_customer_request方法
- [ ] 移除MONITOR_BACKEND_*配置
- [ ] 移除API中间件监控代码
- [ ] 保留本地系统监控功能

### ✅ 功能验证
- [ ] F5 Shape绕过功能正常
- [ ] 设备管理功能正常
- [ ] API接口功能正常
- [ ] 本地监控功能正常
- [ ] 无外部数据发送

### ✅ 文档准备
- [ ] 创建DELIVERY_VERSION.md
- [ ] 更新部署说明
- [ ] 准备客户使用指南

---

## 注意事项

### 1. 备份重要性
- 执行清理前必须备份原始代码
- 建议保留开发版本用于内部监控
- 交付版本仅用于客户部署

### 2. 功能完整性
- 确保绕过功能完全保留
- 确保本地监控功能正常
- 确保API接口完整可用

### 3. 安全考虑
- 移除所有客户数据收集代码
- 确保无外部数据传输
- 保持系统本地监控能力

**总结**: 交付版本保留完整的绕过功能和本地监控，仅移除向监控后台发送客户数据的代码，确保客户隐私和系统独立性。
