# 星巴克F5 Shape风控绕过系统 - 项目结构总结

## 项目完整性状态

**最新检查结果（2025-07-31）：**
- 总检查项目：51项
- 成功项目：51项
- 错误项目：0项
- 警告项目：0项
- **成功率：100.0%**

**项目状态：** ✅ 完全就绪，所有组件完整

## 项目概述

本项目是一个完整的F5 Shape设备指纹风控绕过解决方案，专门针对星巴克app等使用F5 Shape技术的应用进行风控测试和绕过。系统采用独立部署架构，主系统与监控后台完全分离，确保安全性和稳定性。

## 完整项目结构

```
星巴克风控绕过系统/
├── docs/                           # 文档目录
│   ├── README.md                   # 主要文档
│   ├── 给客户的接口说明.md         # 客户接口文档
│   ├── 需求分析说明.md             # 需求分析
│   ├── 代码开发规范.md             # 开发规范
│   ├── 项目结构总结.md             # 本文档
│   └── 其他回顾文档...             # 开发过程回顾
│
├── starbucks/                      # 主系统目录
│   ├── src/                        # 源代码目录
│   │   ├── core/                   # 核心模块
│   │   │   ├── f5_shape_generator.py    # F5指纹生成器
│   │   │   ├── device_manager.py        # 设备管理器
│   │   │   └── bypass_analyzer.py       # 绕过分析器
│   │   ├── api/                    # API接口
│   │   │   ├── main.py             # 主API应用
│   │   │   ├── routes.py           # 路由定义
│   │   │   └── models.py           # 数据模型
│   │   ├── utils/                  # 工具模块
│   │   │   ├── auth.py             # 认证工具
│   │   │   ├── logger.py           # 日志工具
│   │   │   └── config.py           # 配置工具
│   │   └── tests/                  # 测试目录
│   ├── scripts/                    # 部署脚本
│   │   ├── deploy.sh               # 主系统部署脚本
│   │   ├── create_user.sh          # 用户创建脚本
│   │   └── delete_user.sh          # 用户删除脚本
│   ├── abcd.txt                    # F5指纹数据(435个样本)
│   ├── requirements.txt            # Python依赖
│   ├── .env                        # 环境配置
│   ├── run.py                      # 快速启动脚本
│   └── README.md                   # 核心代码说明
│
└── monitor_backend/                # 独立监控后台
    ├── src/                        # 监控后台源码
    │   ├── monitor_app.py          # 监控主应用
    │   ├── templates/              # Web界面模板
    │   │   ├── login.html          # 登录页面
    │   │   └── dashboard.html      # 管理后台
    │   └── static/                 # 静态资源
    │       ├── css/                # 样式文件
    │       │   ├── login.css       # 登录页样式
    │       │   └── dashboard.css   # 后台样式
    │       └── js/                 # JavaScript文件
    │           ├── login.js        # 登录功能
    │           └── dashboard.js    # 后台功能
    ├── scripts/                    # 监控脚本
    │   └── instant_cleanup.sh      # 即用即删脚本
    ├── deploy_monitor.sh           # 监控后台部署脚本
    ├── test_monitor.py             # 监控后台测试脚本
    ├── requirements.txt            # 监控后台依赖
    └── README.md                   # 监控后台说明
```

## 核心功能模块

### 1. 主系统 (starbucks/)

**核心特性：**
- 真实F5 Shape算法实现
- 基于435个真实指纹样本
- 支持30设备并发处理
- 高绕过成功率(90%+)
- 完整的HTTP API接口

**关键文件：**
- `src/core/f5_shape_generator.py`: F5指纹生成核心算法
- `src/api/main.py`: FastAPI主应用
- `abcd.txt`: 435个真实F5指纹样本数据
- `.env`: 生产环境配置

### 2. 监控后台 (monitor_backend/)

**核心特性：**
- 独立部署架构
- 企业级Web管理界面
- 实时监控和日志记录
- 安全威胁检测
- 客户使用统计

**关键文件：**
- `src/monitor_app.py`: 监控后台主应用
- `src/templates/`: Web界面模板
- `src/static/`: 前端资源文件
- `deploy_monitor.sh`: 一键部署脚本

## 部署架构

### 系统端口配置

- **SSH端口**: 28262 (安全访问)
- **主系统端口**: 8094 (Nginx代理)
- **内部API端口**: 8888 (仅本地)
- **监控后台端口**: 9000 (独立访问)

### 服务架构

```
客户端请求 → Nginx(8094) → 主系统(8888) → 监控记录(9000)
                ↓
            负载均衡 → F5指纹生成 → 风控绕过
                ↓
            响应返回 ← 绕过结果 ← 成功率分析
```

## 安全特性

### 1. 权限隔离
- 独立用户账户运行
- 最小权限原则
- 服务间物理隔离

### 2. 数据安全
- 加密存储敏感数据
- 安全的API密钥管理
- 完整的访问日志记录

### 3. 监控防护
- 实时异常检测
- 可疑行为识别
- 自动安全告警

## 客户接口

### API认证
- API密钥认证机制
- JWT令牌支持
- 请求频率限制

### 核心接口
- `/api/bypass/test-service`: 风控绕过测试
- `/api/device/generate`: 设备指纹生成
- `/api/analysis/report`: 绕过效果分析

### 使用示例
```bash
curl -X POST "http://服务器IP:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 5,
    "test_duration": 300,
    "target_app": "starbucks"
  }'
```

## 技术栈

### 后端技术
- **Python 3.8+**: 主要开发语言
- **FastAPI**: Web框架
- **SQLite**: 数据存储
- **Redis**: 缓存系统
- **Nginx**: 反向代理

### 前端技术
- **Bootstrap 5**: UI框架
- **Chart.js**: 数据可视化
- **jQuery**: JavaScript库
- **WebSocket**: 实时通信

### 部署技术
- **Ubuntu 20.04+**: 操作系统
- **systemd**: 服务管理
- **UFW**: 防火墙配置
- **Python venv**: 虚拟环境

## 开发规范

### 代码规范
- 严格禁止使用emoji和特殊符号
- 纯文本输出要求
- 中文注释和文档
- 模块化设计原则

### 文档规范
- 所有markdown文件放在docs/目录
- 完整的API文档
- 详细的部署说明
- 客户使用指南

### 安全规范
- 敏感信息环境变量化
- 定期安全审计
- 访问日志记录
- 异常行为监控

## 维护指南

### 日常维护
```bash
# 检查主系统状态
sudo systemctl status starbucks-api

# 检查监控后台状态
sudo systemctl status monitor-backend

# 查看系统日志
sudo journalctl -u starbucks-api -f
sudo journalctl -u monitor-backend -f
```

### 数据备份
```bash
# 备份数据库
cp /opt/starbucks/starbucks_devices.db /backup/
cp /opt/monitor_backend/data/monitor_logs.db /backup/

# 备份配置文件
cp /opt/starbucks/.env /backup/
cp /opt/monitor_backend/.env /backup/
```

### 性能监控
- CPU使用率监控
- 内存使用监控
- 磁盘空间监控
- 网络流量监控

## 故障排除

### 常见问题
1. **服务启动失败**: 检查端口占用和权限
2. **API调用失败**: 验证API密钥和网络连接
3. **监控后台无法访问**: 检查防火墙和服务状态
4. **指纹生成失败**: 检查abcd.txt文件完整性

### 日志位置
- 主系统日志: `/opt/starbucks/logs/`
- 监控后台日志: `/opt/monitor_backend/logs/`
- 系统服务日志: `journalctl -u service-name`

## 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2025年7月
- **开发者**: YINGAshadow
- **技术支持**: 查看docs目录下的相关文档

---

**注意**: 本系统仅用于合法的安全测试和研究目的，请遵守相关法律法规。
