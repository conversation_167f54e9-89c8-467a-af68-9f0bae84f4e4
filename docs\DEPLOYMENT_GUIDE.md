# 星巴克F5 Shape风控绕过系统 - 安全部署指南

## 重要安全提示

⚠️ **禁止使用root用户部署** ⚠️

为了确保系统安全，本项目严格禁止使用root用户进行部署。所有部署操作都应该使用具有sudo权限的普通用户执行。

## 部署前准备

### 1. 创建部署用户

```bash
# 使用root用户创建部署用户（仅此步骤需要root）
sudo useradd -m -s /bin/bash deployer
sudo passwd deployer

# 添加sudo权限
sudo usermod -aG sudo deployer

# 切换到部署用户
su - deployer
```

### 2. 验证用户权限

```bash
# 检查当前用户
whoami
# 应该显示: deployer (不是root)

# 检查sudo权限
sudo -l
# 应该显示sudo权限列表

# 测试sudo权限
sudo echo "sudo权限正常"
```

### 3. 下载项目代码

```bash
# 切换到用户主目录
cd ~

# 下载或复制项目代码到当前目录
# 确保项目结构如下：
# ~/starbucks-project/
# ├── starbucks/
# ├── monitor_backend/
# ├── docs/
# ├── README.md
# ├── deploy_all.sh
# └── check_project.py
```

## 安全部署流程

### 1. 项目完整性检查

```bash
# 进入项目目录
cd ~/starbucks-project

# 运行项目完整性检查
python3 check_project.py

# 确保显示：成功率: 100.0%
```

### 2. 设置脚本执行权限

```bash
# 设置部署脚本执行权限
chmod +x deploy_all.sh

# 检查脚本权限
ls -la deploy_all.sh
# 应该显示: -rwxr-xr-x
```

### 3. 执行安全部署

```bash
# 使用非root用户执行部署
./deploy_all.sh

# 脚本会自动检查：
# - 禁止root用户运行
# - 验证sudo权限
# - 检查系统环境
# - 执行安全部署
```

## 部署过程说明

### 自动执行的步骤

1. **环境检查**
   - 检测操作系统版本
   - 验证非root用户身份
   - 确认sudo权限
   - 检查项目结构完整性

2. **系统更新**
   - 更新系统包列表
   - 升级系统包

3. **依赖安装**
   - Python 3和相关工具
   - Nginx Web服务器
   - 防火墙工具
   - 其他必需工具

4. **安全配置**
   - 配置防火墙规则
   - 设置端口访问控制
   - 配置SSH安全

5. **系统部署**
   - 部署主系统服务
   - 部署监控后台
   - 配置系统服务

6. **部署验证**
   - 检查服务状态
   - 验证端口监听
   - 确认系统正常运行

## 安全特性

### 用户权限隔离

- **部署用户**: deployer (具有sudo权限)
- **主系统用户**: starbucks (非特权用户)
- **监控后台用户**: monitor-backend (非特权用户)

### 端口安全配置

- **SSH端口**: 28262 (自定义安全端口)
- **主系统端口**: 8094 (Nginx反向代理)
- **监控后台端口**: 9000 (独立访问)

### 防火墙配置

```bash
# 查看防火墙状态
sudo ufw status

# 应该显示：
# Status: active
# To                         Action      From
# --                         ------      ----
# 28262/tcp                  ALLOW       Anywhere
# 8094/tcp                   ALLOW       Anywhere
# 9000/tcp                   ALLOW       Anywhere
```

## 部署后验证

### 1. 服务状态检查

```bash
# 检查主系统服务
sudo systemctl status starbucks-bypass

# 检查监控后台服务
sudo systemctl status monitor-backend

# 检查Nginx服务
sudo systemctl status nginx
```

### 2. 端口监听检查

```bash
# 检查端口监听状态
netstat -tlnp | grep -E ":(8094|9000|28262)"

# 应该显示三个端口都在监听
```

### 3. Web界面访问测试

```bash
# 测试主系统API
curl http://localhost:8094/health

# 测试监控后台
curl http://localhost:9000
```

## 常见问题解决

### 1. 权限问题

**问题**: "Permission denied" 错误
**解决**: 确保使用具有sudo权限的用户，不要使用root用户

```bash
# 检查当前用户
whoami

# 如果是root用户，切换到普通用户
su - deployer
```

### 2. sudo权限问题

**问题**: "sudo: command not found" 或权限不足
**解决**: 添加用户到sudo组

```bash
# 使用root用户添加sudo权限
sudo usermod -aG sudo deployer

# 重新登录用户
su - deployer
```

### 3. 端口占用问题

**问题**: 端口已被占用
**解决**: 检查并停止占用端口的进程

```bash
# 查看端口占用
sudo netstat -tlnp | grep :8094

# 停止占用进程
sudo kill -9 <进程ID>
```

### 4. 防火墙问题

**问题**: 无法访问Web界面
**解决**: 检查防火墙配置

```bash
# 检查防火墙状态
sudo ufw status

# 重新配置防火墙
sudo ufw allow 8094/tcp
sudo ufw allow 9000/tcp
```

## 安全维护

### 定期安全检查

```bash
# 检查系统用户
cat /etc/passwd | grep -E "(starbucks|monitor-backend|deployer)"

# 检查sudo权限
sudo cat /etc/sudoers.d/*

# 检查服务状态
sudo systemctl list-units --type=service | grep -E "(starbucks|monitor)"
```

### 日志监控

```bash
# 查看主系统日志
sudo journalctl -u starbucks-bypass -f

# 查看监控后台日志
sudo journalctl -u monitor-backend -f

# 查看系统安全日志
sudo tail -f /var/log/auth.log
```

### 备份重要数据

```bash
# 备份数据库
sudo cp /opt/starbucks/starbucks_devices.db /backup/
sudo cp /opt/monitor_backend/data/monitor_logs.db /backup/

# 备份配置文件
sudo cp /opt/starbucks/.env /backup/
sudo cp /opt/monitor_backend/.env /backup/
```

## 紧急处理

### 停止所有服务

```bash
# 停止主系统
sudo systemctl stop starbucks-bypass

# 停止监控后台
sudo systemctl stop monitor-backend

# 停止Nginx
sudo systemctl stop nginx
```

### 重置防火墙

```bash
# 重置防火墙规则
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
```

### 清理部署

```bash
# 删除服务用户
sudo userdel -r starbucks
sudo userdel -r monitor-backend

# 删除服务文件
sudo rm -rf /opt/starbucks
sudo rm -rf /opt/monitor_backend

# 删除systemd服务
sudo rm -f /etc/systemd/system/starbucks-bypass.service
sudo rm -f /etc/systemd/system/monitor-backend.service
sudo systemctl daemon-reload
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 检查本文档的常见问题部分
2. 查看系统日志获取详细错误信息
3. 确保严格按照安全部署流程执行
4. 记录详细的错误信息和操作步骤

---

**重要提醒**: 始终使用非root用户进行部署和维护操作，这是确保系统安全的基本要求。
