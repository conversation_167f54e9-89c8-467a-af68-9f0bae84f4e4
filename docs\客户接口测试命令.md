# 客户接口测试命令 - 并发风控绕过测试

## 🎯 测试目标
- **并发能力测试**: 验证30台设备同时请求
- **风控绕过测试**: 验证F5 Shape绕过效果
- **性能测试**: 验证响应时间和成功率

## 🔑 认证信息
```bash
# 您的专用API密钥
API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"

# 服务器地址
SERVER="http://38.150.2.100:8094"
```

## 🚀 直接测试命令

### 1. 系统健康检查（无需密钥）
```bash
curl -X GET "http://38.150.2.100:8094/health"
```

### 2. 基础风控绕过测试（5设备）
```bash
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 5,
      "method": "GET",
      "concurrent_limit": 3,
      "delay_between_requests": 0.2
    }
  }'
```

### 3. 并发能力测试（30设备同时）
```bash
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 30,
      "delay_between_requests": 0
    }
  }'
```

### 4. 客户自定义目标测试
```bash
# 客户替换target_url为自己要测试的网站
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://客户的目标网站.com/api/endpoint",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 15,
      "delay_between_requests": 0.1
    }
  }'
```

### 5. POST请求测试（带数据）
```bash
curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://客户的目标网站.com/api/login",
    "test_config": {
      "device_count": 30,
      "method": "POST",
      "headers": {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0"
      },
      "data": {
        "username": "test",
        "password": "test123"
      },
      "concurrent_limit": 10,
      "delay_between_requests": 0.1
    }
  }'
```

## 🔥 压力测试命令

### 连续多次测试（验证稳定性）
```bash
# 连续执行5次并发测试
for i in {1..5}; do
  echo "第 $i 次测试..."
  curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
    -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
    -H "Content-Type: application/json" \
    -d '{
      "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
      "test_config": {
        "device_count": 30,
        "method": "GET",
        "concurrent_limit": 15
      }
    }'
  echo "等待2秒..."
  sleep 2
done
```

### 同时发起多个测试（极限压力）
```bash
# 同时发起3个30设备的测试
for i in {1..3}; do
  curl -X POST "http://38.150.2.100:8094/api/bypass/test-service" \
    -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
    -H "Content-Type: application/json" \
    -d '{
      "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
      "test_config": {
        "device_count": 30,
        "method": "GET",
        "concurrent_limit": 10
      }
    }' &
done
wait
echo "所有测试完成"
```

## 📊 测试结果解读

### 成功的测试结果示例
```json
{
  "success": true,
  "message": "风控绕过测试完成",
  "test_id": "test_20250801_123456",
  "summary": {
    "total_requests": 30,
    "successful_requests": 28,
    "failed_requests": 2,
    "success_rate": 0.93,
    "average_response_time": 1.2,
    "total_test_time": 15.5
  },
  "bypass_analysis": {
    "f5_detection_bypassed": true,
    "fingerprint_uniqueness": 100,
    "risk_score_reduction": 87,
    "detection_patterns": []
  },
  "device_results": [
    {
      "device_index": 0,
      "success": true,
      "response_time": 1.1,
      "status_code": 200,
      "fingerprint_used": "device_0_fingerprint"
    }
  ]
}
```

### 关键指标说明
- **success_rate**: 成功率，应该 > 85%
- **average_response_time**: 平均响应时间，应该 < 3秒
- **f5_detection_bypassed**: F5检测是否被绕过
- **fingerprint_uniqueness**: 指纹唯一性百分比

## 🛠️ 其他测试接口

### 获取设备指纹
```bash
curl -X POST "http://38.150.2.100:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 5}'
```

### 查看设备状态
```bash
curl -X GET "http://38.150.2.100:8094/api/v1/devices" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
```

### 获取单个设备指纹
```bash
curl -X GET "http://38.150.2.100:8094/api/v1/fingerprint/0" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
```

## 📝 测试脚本（一键测试）

### Windows批处理脚本
```batch
@echo off
echo F5 Shape风控绕过系统测试
echo ========================

set API_KEY=SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
set SERVER=http://38.150.2.100:8094

echo 1. 健康检查...
curl -X GET "%SERVER%/health"

echo.
echo 2. 基础测试（5设备）...
curl -X POST "%SERVER%/api/bypass/test-service" ^
  -H "X-API-Key: %API_KEY%" ^
  -H "Content-Type: application/json" ^
  -d "{\"target_url\": \"https://app.starbucks.com.cn/bff/ordering/product/list\", \"test_config\": {\"device_count\": 5, \"method\": \"GET\", \"concurrent_limit\": 3}}"

echo.
echo 3. 并发测试（30设备）...
curl -X POST "%SERVER%/api/bypass/test-service" ^
  -H "X-API-Key: %API_KEY%" ^
  -H "Content-Type: application/json" ^
  -d "{\"target_url\": \"https://app.starbucks.com.cn/bff/ordering/product/list\", \"test_config\": {\"device_count\": 30, \"method\": \"GET\", \"concurrent_limit\": 15}}"

echo.
echo 测试完成！
pause
```

### Linux/Mac脚本
```bash
#!/bin/bash
echo "F5 Shape风控绕过系统测试"
echo "========================"

API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
SERVER="http://38.150.2.100:8094"

echo "1. 健康检查..."
curl -X GET "$SERVER/health"

echo -e "\n2. 基础测试（5设备）..."
curl -X POST "$SERVER/api/bypass/test-service" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 5,
      "method": "GET",
      "concurrent_limit": 3
    }
  }'

echo -e "\n3. 并发测试（30设备）..."
curl -X POST "$SERVER/api/bypass/test-service" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 15
    }
  }'

echo -e "\n测试完成！"
```

## 🎯 给客户的测试建议

### 测试步骤
1. **先执行健康检查** - 确认系统可用
2. **基础功能测试** - 5设备测试验证基本功能
3. **并发能力测试** - 30设备测试验证并发能力
4. **自定义目标测试** - 使用客户真实目标网站
5. **压力测试** - 连续多次测试验证稳定性

### 成功标准
- **成功率** > 85%
- **响应时间** < 3秒
- **F5绕过** = true
- **30设备并发** 正常运行

### 如果测试失败
1. 检查API密钥是否正确
2. 检查网络连接
3. 联系技术支持：7x24小时在线
4. 查看API文档：http://38.150.2.100:8094/docs
