#!/bin/bash

# 星巴克风控绕过系统 - 监控后台用户创建脚本
# 作者：YINGAshadow
# 创建时间：2025-8-1
# 功能：创建监控后台专用部署用户

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_USER="monitor"
USER_HOME="/home/<USER>"
USER_PROJECT_DIR="$USER_HOME/monitor_backend"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        log_error "使用命令: sudo $0"
        exit 1
    fi
}

# 检查系统
check_system() {
    log_step "检查系统环境"
    
    if ! command -v useradd &> /dev/null; then
        log_error "系统不支持useradd命令"
        exit 1
    fi
    
    log_info "系统检查通过"
}

# 创建部署用户
create_user() {
    log_step "创建监控后台部署用户"
    
    if id "$DEPLOY_USER" &>/dev/null; then
        log_warn "用户 $DEPLOY_USER 已存在，跳过创建"
        return 0
    fi
    
    # 创建用户
    useradd -m -s /bin/bash "$DEPLOY_USER"
    
    # 设置用户密码（可选）
    echo "$DEPLOY_USER:Monitor2025#Backend!" | chpasswd
    
    # 添加sudo权限
    echo "$DEPLOY_USER ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/$DEPLOY_USER
    
    log_info "用户 $DEPLOY_USER 创建成功"
    log_info "用户主目录: $USER_HOME"
    log_info "默认密码: Monitor2025#Backend!"
}

# 复制项目文件
copy_project_files() {
    log_step "复制监控后台项目文件"
    
    # 创建项目目录
    mkdir -p "$USER_PROJECT_DIR"
    
    # 从脚本位置向上查找项目根目录
    if [[ -f "$SCRIPT_DIR/../src/monitor_app.py" ]]; then
        # 脚本在 monitor_backend/scripts/ 目录下
        PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
        log_info "检测到项目根目录: $PROJECT_ROOT"
    elif [[ -f "$SCRIPT_DIR/../../monitor_backend/src/monitor_app.py" ]]; then
        # 脚本在其他位置
        PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")/monitor_backend"
        log_info "检测到项目根目录: $PROJECT_ROOT"
    else
        log_error "未找到监控后台项目文件"
        log_error "查找路径: $SCRIPT_DIR/../src/monitor_app.py"
        log_error "查找路径: $SCRIPT_DIR/../../monitor_backend/src/monitor_app.py"
        exit 1
    fi
    
    # 复制项目文件
    if [[ -n "$PROJECT_ROOT" && -d "$PROJECT_ROOT" ]]; then
        log_info "从 $PROJECT_ROOT 复制项目文件"
        
        # 复制所有文件，包括隐藏文件
        cp -r "$PROJECT_ROOT"/* "$USER_PROJECT_DIR/" 2>/dev/null || true
        cp "$PROJECT_ROOT"/.[^.]* "$USER_PROJECT_DIR/" 2>/dev/null || true
        
        # 验证关键文件是否复制成功
        if [[ -f "$USER_PROJECT_DIR/src/monitor_app.py" ]]; then
            log_info "项目文件复制成功，检测到核心文件"
        else
            log_warn "项目文件复制可能不完整"
        fi
        
        # 验证.env文件是否复制成功
        if [[ -f "$USER_PROJECT_DIR/.env" ]]; then
            log_info "环境配置文件复制成功: .env"
        else
            log_warn "未找到.env文件，将创建默认配置"
            create_default_env
        fi
    else
        log_error "项目根目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$USER_PROJECT_DIR"
    if [[ -d "$USER_PROJECT_DIR/scripts" ]]; then
        chmod +x "$USER_PROJECT_DIR/scripts/"*.sh 2>/dev/null || true
    fi
    
    log_info "项目文件复制完成: $USER_PROJECT_DIR"
}

# 创建默认环境配置
create_default_env() {
    log_info "创建默认环境配置文件"
    
    cat > "$USER_PROJECT_DIR/.env" << 'EOF'
# 监控后台环境配置

# 服务器配置
HOST=0.0.0.0
PORT=9000
DEBUG=false

# 安全配置
SECRET_KEY=Monitor2025BackendSecretKey789
ACCESS_TOKEN_EXPIRE_MINUTES=60
ADMIN_USERNAME=monitor_admin
ADMIN_PASSWORD=MonitorAdmin2025#Backend!

# 数据库配置
DATABASE_URL=sqlite:///./monitor_data.db

# 主系统集成配置
MAIN_SYSTEM_URL=http://localhost:8888
MAIN_SYSTEM_TOKEN=monitor_backend_secret_token_2025

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/monitor_backend.log

# 监控配置
MONITOR_INTERVAL=60
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=80
ALERT_THRESHOLD_DISK=90
EOF
    
    chown "$DEPLOY_USER:$DEPLOY_USER" "$USER_PROJECT_DIR/.env"
    chmod 600 "$USER_PROJECT_DIR/.env"
}

# 配置用户环境
configure_user_environment() {
    log_step "配置用户环境"

    # 创建必要的目录
    mkdir -p "$USER_PROJECT_DIR/logs"
    mkdir -p "$USER_PROJECT_DIR/data"

    # 设置目录权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$USER_PROJECT_DIR"

    log_info "用户环境配置完成"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo ""
    echo "=========================================="
    echo "监控后台用户创建完成"
    echo "=========================================="
    echo ""
    echo "用户信息:"
    echo "  用户名: $DEPLOY_USER"
    echo "  主目录: $USER_HOME"
    echo "  项目目录: $USER_PROJECT_DIR"
    echo "  默认密码: Monitor2025#Backend!"
    echo ""
    echo "下一步操作:"
    echo "  1. 切换到监控用户: sudo su - $DEPLOY_USER"
    echo "  2. 运行部署脚本: cd monitor_backend && sudo ./deploy_monitor.sh"
    echo "  3. 或直接运行: sudo -u $DEPLOY_USER $USER_PROJECT_DIR/deploy_monitor.sh"
    echo ""
    echo "管理命令:"
    echo "  删除用户: sudo $SCRIPT_DIR/delete_monitor_user.sh"
    echo "  重置密码: sudo passwd $DEPLOY_USER"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "星巴克风控绕过系统 - 监控后台用户创建"
    echo "=========================================="
    
    check_permissions
    check_system
    create_user
    copy_project_files
    configure_user_environment
    show_deployment_info
    
    log_info "监控后台用户创建完成！"
}

# 执行主函数
main "$@"
