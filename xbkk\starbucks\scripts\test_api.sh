#!/bin/bash
# API测试脚本
# 作者：YINGAshadow
# 创建时间：2025-7-29
# 功能：测试API接口功能

# 配置
API_BASE="http://localhost:8094"
DEVICE_COUNT=5

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[测试]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_test "检查服务状态"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/health")
    
    if [[ "$response" == "200" ]]; then
        log_success "服务运行正常"
    else
        log_error "服务未运行或无法访问，HTTP状态码: $response"
        exit 1
    fi
}

# 测试根路径
test_root() {
    log_test "测试根路径"
    
    response=$(curl -s "$API_BASE/")
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "根路径测试通过"
    else
        log_error "根路径测试失败"
    fi
}

# 测试健康检查
test_health() {
    log_test "测试健康检查"
    
    response=$(curl -s "$API_BASE/health")
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "健康检查测试通过"
    else
        log_error "健康检查测试失败"
    fi
}

# 测试生成指纹
test_generate_fingerprint() {
    log_test "测试生成设备指纹"
    
    response=$(curl -s -X POST "$API_BASE/api/v1/fingerprint/generate" \
        -H "Content-Type: application/json" \
        -d "{\"device_count\": $DEVICE_COUNT}")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "指纹生成测试通过"
    else
        log_error "指纹生成测试失败"
    fi
}

# 测试获取单个设备指纹
test_get_device_fingerprint() {
    log_test "测试获取单个设备指纹"
    
    device_index=0
    response=$(curl -s "$API_BASE/api/v1/fingerprint/$device_index")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "获取设备指纹测试通过"
    else
        log_error "获取设备指纹测试失败"
    fi
}

# 测试设备列表
test_get_devices() {
    log_test "测试获取设备列表"
    
    response=$(curl -s "$API_BASE/api/v1/devices")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "获取设备列表测试通过"
    else
        log_error "获取设备列表测试失败"
    fi
}

# 测试设备操作
test_device_operation() {
    log_test "测试设备操作"
    
    device_index=1
    
    # 测试重新生成设备
    response=$(curl -s -X POST "$API_BASE/api/v1/devices/operation" \
        -H "Content-Type: application/json" \
        -d "{\"device_index\": $device_index, \"operation\": \"regenerate\"}")
    
    echo "重新生成响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "设备重新生成测试通过"
    else
        log_error "设备重新生成测试失败"
    fi
    
    # 测试释放设备
    response=$(curl -s -X POST "$API_BASE/api/v1/devices/operation" \
        -H "Content-Type: application/json" \
        -d "{\"device_index\": $device_index, \"operation\": \"release\"}")
    
    echo "释放设备响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "设备释放测试通过"
    else
        log_error "设备释放测试失败"
    fi
}

# 测试风控绕过
test_bypass() {
    log_test "测试风控绕过"
    
    # 测试单个设备 - 星巴克产品列表API
    response=$(curl -s -X POST "$API_BASE/api/v1/test/bypass" \
        -H "Content-Type: application/json" \
        -d "{\"device_index\": 0, \"test_endpoint\": \"https://app.starbucks.com.cn/bff/ordering/product/list\"}")

    echo "单设备测试响应: $response"

    if echo "$response" | grep -q "success.*true"; then
        log_success "单设备风控测试通过"
    else
        log_error "单设备风控测试失败"
    fi

    # 测试并发设备 - 星巴克门店列表API
    response=$(curl -s -X POST "$API_BASE/api/v1/test/bypass" \
        -H "Content-Type: application/json" \
        -d "{\"concurrent_count\": 3, \"test_endpoint\": \"https://app.starbucks.com.cn/bff/ordering/store/list\"}")

    echo "并发测试响应: $response"

    if echo "$response" | grep -q "success.*true"; then
        log_success "并发风控测试通过"
    else
        log_error "并发风控测试失败"
    fi

    # 测试用户信息API
    response=$(curl -s -X POST "$API_BASE/api/v1/test/bypass" \
        -H "Content-Type: application/json" \
        -d "{\"concurrent_count\": 2, \"test_endpoint\": \"https://app.starbucks.com.cn/bff/user/profile\"}")

    echo "用户信息API测试响应: $response"

    if echo "$response" | grep -q "success.*true"; then
        log_success "用户信息API测试通过"
    else
        log_error "用户信息API测试失败"
    fi
}

# 测试清理设备
test_cleanup() {
    log_test "测试清理被封设备"
    
    response=$(curl -s -X POST "$API_BASE/api/v1/devices/cleanup")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q "success.*true"; then
        log_success "设备清理测试通过"
    else
        log_error "设备清理测试失败"
    fi
}

# 性能测试
performance_test() {
    log_test "执行性能测试"
    
    log_info "测试并发请求性能..."
    
    # 并发生成指纹
    for i in {1..10}; do
        curl -s -X POST "$API_BASE/api/v1/fingerprint/generate" \
            -H "Content-Type: application/json" \
            -d "{\"device_count\": 1}" > /dev/null &
    done
    
    wait
    log_success "并发指纹生成测试完成"
    
    # 并发获取设备列表
    for i in {1..10}; do
        curl -s "$API_BASE/api/v1/devices" > /dev/null &
    done
    
    wait
    log_success "并发设备列表获取测试完成"
}

# 主测试函数
main() {
    log_info "开始API接口测试"
    echo "测试目标: $API_BASE"
    echo "设备数量: $DEVICE_COUNT"
    echo ""
    
    check_service
    echo ""
    
    test_root
    echo ""
    
    test_health
    echo ""
    
    test_generate_fingerprint
    echo ""
    
    test_get_device_fingerprint
    echo ""
    
    test_get_devices
    echo ""
    
    test_device_operation
    echo ""
    
    test_bypass
    echo ""
    
    test_cleanup
    echo ""
    
    performance_test
    echo ""
    
    log_info "API接口测试完成"
}

# 执行测试
main "$@"
