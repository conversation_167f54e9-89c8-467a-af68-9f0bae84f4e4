# 重新部署端口修复回顾

**作者**: YINGAshadow  
**日期**: 2025-8-1  
**项目**: 星巴克F5 Shape风控绕过系统  
**操作**: 重新部署时的端口配置修复  

## 问题分析

### 1. 部署失败原因
用户在重新部署时遇到以下问题：
- .env文件未找到，导致端口配置使用默认值8000
- systemd服务启动失败，因为缺少必要的环境配置

### 2. 根本原因
- deploy.sh脚本中缺少复制.env文件的步骤
- 应用默认端口配置与Nginx代理配置不匹配

## 修复措施

### 1. 更新部署脚本
**文件**: `starbucks/scripts/deploy.sh`

**添加内容**:
```bash
# 复制环境配置文件
if [[ -f "$USER_PROJECT_DIR/.env" ]]; then
    cp "$USER_PROJECT_DIR/.env" "$PROJECT_DIR/"
    log_info "已复制环境配置文件: .env"
else
    log_warning "未找到.env文件，将使用默认配置"
fi
```

### 2. 创建环境配置脚本
创建了 `create_env_file.sh` 脚本，用于快速创建正确的.env配置文件。

## 解决方案

### 方案1: 手动创建.env文件
在服务器上执行以下命令：

```bash
# 1. 创建.env文件
cat > /home/<USER>/starbucks/.env << 'EOF'
# 星巴克F5 Shape风控绕过系统 - 生产环境配置

# 服务器配置
HOST=0.0.0.0
PORT=8888
DEBUG=false

# 安全配置
SECRET_KEY=SB2025F5ShapeBypassSystemSecretKey789
ACCESS_TOKEN_EXPIRE_MINUTES=60
ADMIN_PASSWORD=SBAdmin2025#F5Shape!

# 数据库配置
DATABASE_URL=sqlite:///./starbucks_devices.db

# 客户API密钥配置
CUSTOMER_API_KEY_1=SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
CUSTOMER_ID_1=customer_001

CUSTOMER_API_KEY_2=SB_API_2025_CUSTOMER_002_F5SHAPE_BYPASS
CUSTOMER_ID_2=customer_002

CUSTOMER_API_KEY_3=SB_API_2025_CUSTOMER_003_F5SHAPE_BYPASS
CUSTOMER_ID_3=customer_003

# 默认API密钥
DEFAULT_API_KEY=SB_DEFAULT_API_2025_F5SHAPE_BYPASS

# F5 Shape指纹配置
F5_FINGERPRINT_DATA_FILE=abcd.txt
F5_ALGORITHM_VERSION=2025.1
F5_SIGNATURE_KEY=f5_shape_signature_key_2025

# API认证配置
API_KEY_CUSTOMER_001=SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
API_KEY_CUSTOMER_002=SB_API_2025_CUSTOMER_002_F5SHAPE_BYPASS
API_KEY_CUSTOMER_003=SB_API_2025_CUSTOMER_003_F5SHAPE_BYPASS

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/starbucks_fingerprint.log

# 性能配置
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30
RETRY_COUNT=3
RETRY_DELAY=1

# 监控后台集成配置
MONITOR_BACKEND_ENABLED=true
MONITOR_BACKEND_URL=http://localhost:9000
MONITOR_BACKEND_TOKEN=monitor_backend_secret_token_2025
EOF

# 2. 设置文件权限
chmod 600 /home/<USER>/starbucks/.env
chown sbdeploy:sbdeploy /home/<USER>/starbucks/.env

# 3. 重新运行部署脚本
sudo ./starbucks/scripts/deploy.sh
```

### 方案2: 使用配置脚本
```bash
# 1. 上传配置脚本到服务器
# (在本地执行)
scp -P 28262 create_env_file.sh sbdeploy@************:/home/<USER>/

# 2. 在服务器上执行
chmod +x create_env_file.sh
sudo ./create_env_file.sh

# 3. 重新运行部署脚本
sudo ./starbucks/scripts/deploy.sh
```

## 部署验证步骤

### 1. 检查.env文件
```bash
# 确认.env文件存在且配置正确
ls -la /home/<USER>/starbucks/.env
cat /home/<USER>/starbucks/.env | grep PORT
```

### 2. 检查服务状态
```bash
# 查看服务状态
sudo systemctl status starbucks-fingerprint

# 查看服务日志
sudo journalctl -u starbucks-fingerprint -f
```

### 3. 检查端口监听
```bash
# 检查应用是否监听8888端口
sudo netstat -tlnp | grep :8888

# 检查Nginx是否监听8094端口
sudo netstat -tlnp | grep :8094
```

### 4. 测试API接口
```bash
# 内部测试
curl http://localhost:8888/health

# 外部测试
curl http://localhost:8094/health

# 远程测试
curl http://************:8094/health
```

## 正确的端口配置

### 1. 应用配置
- **内部端口**: 8888 (应用监听)
- **外部端口**: 8094 (Nginx代理)

### 2. 网络架构
```
客户请求 -> Nginx(8094) -> 应用(8888) -> F5绕过处理
```

### 3. 防火墙配置
```bash
# 开放的端口
28262/tcp  # SSH访问
8094/tcp   # Web接口（外部访问）

# 内部端口（仅本地访问）
8888/tcp   # 应用内部端口
```

## 客户测试接口

### 1. 基础测试
```bash
# 健康检查
curl "http://************:8094/health"

# API文档
curl "http://************:8094/docs"
```

### 2. 功能测试
```bash
# 设备指纹生成
curl -X POST "http://************:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 1,
    "target_url": "https://www.starbucks.com"
  }'

# 风控绕过测试
curl -X POST "http://************:8094/api/bypass/test_service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://www.starbucks.com",
    "test_count": 3,
    "bypass_level": "standard"
  }'
```

## 总结

### 1. 问题解决
- [完成] 识别了.env文件缺失的问题
- [完成] 更新了部署脚本以包含环境文件复制
- [完成] 提供了手动创建.env文件的方案
- [完成] 创建了自动化配置脚本

### 2. 部署流程
1. 创建正确的.env配置文件
2. 重新运行部署脚本
3. 验证服务启动和端口监听
4. 测试API接口功能

### 3. 客户服务就绪
修复后的系统将提供：
- 外部访问地址: http://************:8094
- API文档地址: http://************:8094/docs
- 客户API密钥: 已配置3个测试密钥

**操作状态**: 解决方案已提供  
**部署状态**: 等待重新部署  
**配置验证**: 待验证
