# 星巴克F5 Shape风控绕过系统 - 客户接口并发测试方案

**系统名称**: 星巴克设备指纹风控绕过系统  
**版本**: v1.0  
**测试重点**: 并发能力 + 风控绕过效果  
**文档日期**: 2025-8-1  

## 🎯 客户核心需求

客户主要关心两个核心问题：
1. **并发能力** - 系统能否支持多设备同时请求
2. **风控绕过** - 能否有效绕过目标网站的F5 Shape风控

## 🔑 认证信息

### API密钥配置
```bash
# 客户专用API密钥（请为每个客户分配独立密钥）
客户A测试密钥: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS
客户B测试密钥: SB_API_2025_CUSTOMER_002_F5SHAPE_BYPASS
客户C测试密钥: SB_API_2025_CUSTOMER_003_F5SHAPE_BYPASS

# 默认演示密钥（可用于初步测试）
演示密钥: demo_api_key_change_in_production
```

### 服务器信息
```bash
# 您的服务器地址（请替换为实际IP）
主系统API: http://************:8094
API文档: http://************:8094/docs
系统状态: http://************:8094/health
```

## 🚀 核心测试接口

### 1. 风控绕过测试服务（主要接口）
```bash
接口: POST /api/bypass/test-service
功能: 测试目标网站的F5 Shape风控绕过效果
认证: X-API-Key 头部认证
```

**基础测试示例**:
```bash
curl -X POST "http://************:8094/api/bypass/test-service" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/bff/ordering/product/list",
    "test_config": {
      "device_count": 30,
      "method": "GET",
      "concurrent_limit": 10,
      "delay_between_requests": 0.1
    }
  }'
```

### 2. 系统健康检查
```bash
# 无需认证，快速检查系统状态
curl -X GET "http://************:8094/health"
```

### 3. 设备指纹生成
```bash
curl -X POST "http://************:8094/api/v1/fingerprint/generate" \
  -H "X-API-Key: SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS" \
  -H "Content-Type: application/json" \
  -d '{"device_count": 5}'
```

## 🔥 并发测试方案

### 方案一：基础并发测试（推荐给客户）
**目标**: 验证系统30设备并发能力

```bash
#!/bin/bash
# 客户并发测试脚本

API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
SERVER_URL="http://************:8094"
TARGET_URL="https://客户要测试的目标网站.com"

echo "开始并发测试..."
echo "测试时间: $(date)"

# 单次30设备并发测试
curl -X POST "$SERVER_URL/api/bypass/test-service" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"target_url\": \"$TARGET_URL\",
    \"test_config\": {
      \"device_count\": 30,
      \"method\": \"GET\",
      \"concurrent_limit\": 30,
      \"delay_between_requests\": 0
    }
  }"

echo "并发测试完成"
```

### 方案二：压力测试（高级客户）
**目标**: 验证系统在高负载下的表现

```bash
#!/bin/bash
# 高并发压力测试

API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
SERVER_URL="http://************:8094"
TARGET_URL="https://客户要测试的目标网站.com"

echo "开始压力测试..."

# 同时发起10个并发请求，每个请求30设备
for i in {1..10}; do
  echo "启动测试批次 $i"
  curl -X POST "$SERVER_URL/api/bypass/test-service" \
    -H "X-API-Key: $API_KEY" \
    -H "Content-Type: application/json" \
    -d "{
      \"target_url\": \"$TARGET_URL\",
      \"test_config\": {
        \"device_count\": 30,
        \"method\": \"GET\",
        \"concurrent_limit\": 15
      }
    }" &
done

# 等待所有测试完成
wait
echo "压力测试完成"
```

### 方案三：持续监控测试
**目标**: 长时间运行验证系统稳定性

```bash
#!/bin/bash
# 持续监控测试脚本

API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
SERVER_URL="http://************:8094"
TARGET_URL="https://客户要测试的目标网站.com"

echo "开始持续监控测试..."
echo "测试将运行1小时，每5分钟执行一次"

for i in {1..12}; do
  echo "第 $i 轮测试 - $(date)"
  
  # 执行测试
  RESULT=$(curl -s -X POST "$SERVER_URL/api/bypass/test-service" \
    -H "X-API-Key: $API_KEY" \
    -H "Content-Type: application/json" \
    -d "{
      \"target_url\": \"$TARGET_URL\",
      \"test_config\": {
        \"device_count\": 30,
        \"method\": \"GET\",
        \"concurrent_limit\": 10
      }
    }")
  
  # 提取成功率
  SUCCESS_RATE=$(echo "$RESULT" | grep -o '"success_rate":[0-9.]*' | cut -d':' -f2)
  echo "成功率: $SUCCESS_RATE"
  
  # 等待5分钟
  sleep 300
done

echo "持续监控测试完成"
```

## 🐍 Python集成示例

### 客户集成代码
```python
import requests
import time
import concurrent.futures
from typing import Dict, List

class F5BypassClient:
    """F5 Shape风控绕过客户端"""
    
    def __init__(self, api_key: str, base_url: str = "http://************:8094"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        })
    
    def health_check(self) -> Dict:
        """系统健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        return response.json()
    
    def test_bypass(self, target_url: str, device_count: int = 30, 
                   concurrent_limit: int = 10) -> Dict:
        """风控绕过测试"""
        data = {
            "target_url": target_url,
            "test_config": {
                "device_count": device_count,
                "method": "GET",
                "concurrent_limit": concurrent_limit,
                "delay_between_requests": 0.1
            }
        }
        
        response = self.session.post(
            f"{self.base_url}/api/bypass/test-service",
            json=data
        )
        return response.json()
    
    def concurrent_test(self, target_url: str, test_count: int = 5) -> List[Dict]:
        """并发测试"""
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=test_count) as executor:
            futures = [
                executor.submit(self.test_bypass, target_url)
                for _ in range(test_count)
            ]
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"测试失败: {e}")
        
        return results

# 使用示例
if __name__ == "__main__":
    # 初始化客户端
    client = F5BypassClient("SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS")
    
    # 健康检查
    health = client.health_check()
    print(f"系统状态: {health}")
    
    # 单次测试
    result = client.test_bypass("https://目标网站.com")
    print(f"绕过成功率: {result['summary']['success_rate']:.2%}")
    
    # 并发测试
    concurrent_results = client.concurrent_test("https://目标网站.com", 3)
    avg_success_rate = sum(r['summary']['success_rate'] for r in concurrent_results) / len(concurrent_results)
    print(f"并发测试平均成功率: {avg_success_rate:.2%}")
```

## 📊 测试结果评估标准

### 成功指标
- **并发能力**: 支持30设备同时请求
- **响应时间**: 平均响应时间 < 2秒
- **成功率**: 风控绕过成功率 > 85%
- **稳定性**: 连续测试1小时无异常

### 测试报告格式
```json
{
  "test_summary": {
    "total_requests": 30,
    "successful_requests": 27,
    "success_rate": 0.90,
    "average_response_time": 1.2,
    "concurrent_devices": 30
  },
  "bypass_analysis": {
    "f5_detection_bypassed": true,
    "fingerprint_uniqueness": 100,
    "risk_score_reduction": 85
  }
}
```

## 🎯 给客户的测试建议

### 第一阶段：快速验证（10分钟）
1. 健康检查确认系统可用
2. 单次基础绕过测试
3. 查看测试报告和成功率

### 第二阶段：业务验证（30分钟）
1. 使用客户真实目标网站
2. 30设备并发测试
3. 分析绕过效果和响应时间

### 第三阶段：集成测试（1小时）
1. 客户系统集成测试
2. 长时间稳定性验证
3. 性能压力测试

## 📞 技术支持

如客户在测试过程中遇到问题，请提供：
- **API文档**: http://************:8094/docs
- **技术支持**: 7x24小时在线支持
- **响应时间**: 2小时内响应技术问题
- **成功率保证**: 绕过成功率85%以上

## 🔧 实际部署配置

### 服务器环境变量设置
```bash
# 在您的服务器上设置客户API密钥
export CUSTOMER_API_KEY_1="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
export CUSTOMER_ID_1="customer_001"

export CUSTOMER_API_KEY_2="SB_API_2025_CUSTOMER_002_F5SHAPE_BYPASS"
export CUSTOMER_ID_2="customer_002"

export CUSTOMER_API_KEY_3="SB_API_2025_CUSTOMER_003_F5SHAPE_BYPASS"
export CUSTOMER_ID_3="customer_003"

# 默认演示密钥
export DEFAULT_API_KEY="demo_api_key_change_in_production"
```

### 客户测试脚本模板
创建 `customer_test_template.sh` 供客户使用：

```bash
#!/bin/bash
# 客户专用测试脚本模板

# 客户配置区域（客户需要修改这部分）
API_KEY="SB_API_2025_CUSTOMER_001_F5SHAPE_BYPASS"
SERVER_URL="http://************:8094"
TARGET_URL="https://要测试的目标网站.com"

echo "=========================================="
echo "F5 Shape风控绕过系统 - 客户测试"
echo "测试时间: $(date)"
echo "=========================================="

# 1. 系统健康检查
echo "1. 检查系统状态..."
HEALTH=$(curl -s "$SERVER_URL/health")
echo "系统状态: $HEALTH"

# 2. 基础功能测试
echo ""
echo "2. 基础绕过测试..."
curl -X POST "$SERVER_URL/api/bypass/test-service" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"target_url\": \"$TARGET_URL\",
    \"test_config\": {
      \"device_count\": 5,
      \"method\": \"GET\",
      \"concurrent_limit\": 3
    }
  }"

# 3. 并发能力测试
echo ""
echo "3. 并发能力测试（30设备）..."
curl -X POST "$SERVER_URL/api/bypass/test-service" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"target_url\": \"$TARGET_URL\",
    \"test_config\": {
      \"device_count\": 30,
      \"method\": \"GET\",
      \"concurrent_limit\": 15,
      \"delay_between_requests\": 0.1
    }
  }"

echo ""
echo "=========================================="
echo "测试完成！"
echo "=========================================="
```

## 📋 客户交付清单

### 必须提供给客户的文件
1. **测试脚本**: `customer_test_template.sh`
2. **Python示例**: `f5_bypass_client.py`
3. **API文档**: 接口说明和参数详解
4. **测试报告模板**: 结果分析格式

### 必须告知客户的信息
1. **服务器地址**: http://************:8094
2. **专用API密钥**: 为每个客户分配的唯一密钥
3. **技术支持联系方式**: 7x24小时支持
4. **SLA承诺**: 99.5%可用性，85%绕过成功率

## 🚨 安全注意事项

### 客户密钥管理
- 每个客户使用独立API密钥
- 定期轮换密钥（建议每月）
- 监控异常使用模式
- 记录所有API调用日志

### 系统保护措施
- 限制每个密钥的调用频率
- 监控并发请求数量
- 检测异常流量模式
- 自动封禁滥用密钥

---

**重要提醒**:
1. 请为每个客户分配独立的API密钥
2. 建议客户先用演示网站测试，再用真实目标
3. 监控客户使用情况，防止滥用
4. 定期更新指纹算法保持绕过效果
5. 保持与客户的技术沟通，及时解决问题
