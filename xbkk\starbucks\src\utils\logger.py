#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
作者：YINGAshadow
创建时间：2025-7-29
功能：配置和管理系统日志
"""

import logging
import os
from logging.handlers import RotatingFileHandler
from datetime import datetime

from ..config.settings import settings


def setup_logger(name: str) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # 创建日志目录
    log_dir = settings.get_log_dir()
    
    # 文件处理器
    file_handler = RotatingFileHandler(
        settings.LOG_FILE,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def log_api_request(endpoint: str, method: str, params: dict = None, response_time: float = 0):
    """
    记录API请求日志
    
    Args:
        endpoint: 接口端点
        method: 请求方法
        params: 请求参数
        response_time: 响应时间
    """
    logger = logging.getLogger("api_requests")
    
    log_message = f"接口调用 - {method} {endpoint}"
    if params:
        log_message += f" - 参数: {params}"
    if response_time > 0:
        log_message += f" - 响应时间: {response_time:.3f}秒"
    
    logger.info(log_message)


def log_device_operation(device_index: int, operation: str, result: bool, details: str = ""):
    """
    记录设备操作日志
    
    Args:
        device_index: 设备索引
        operation: 操作类型
        result: 操作结果
        details: 详细信息
    """
    logger = logging.getLogger("device_operations")
    
    status = "成功" if result else "失败"
    log_message = f"设备操作 - 设备{device_index} - {operation} - {status}"
    
    if details:
        log_message += f" - {details}"
    
    if result:
        logger.info(log_message)
    else:
        logger.error(log_message)


def log_bypass_test(device_index: int, endpoint: str, success: bool, response_time: float, error: str = ""):
    """
    记录风控测试日志
    
    Args:
        device_index: 设备索引
        endpoint: 测试端点
        success: 是否成功
        response_time: 响应时间
        error: 错误信息
    """
    logger = logging.getLogger("bypass_tests")
    
    status = "成功" if success else "失败"
    log_message = f"风控测试 - 设备{device_index} - {endpoint} - {status} - {response_time:.3f}秒"
    
    if error:
        log_message += f" - 错误: {error}"
    
    if success:
        logger.info(log_message)
    else:
        logger.warning(log_message)


def log_system_event(event: str, level: str = "INFO", details: str = ""):
    """
    记录系统事件日志
    
    Args:
        event: 事件名称
        level: 日志级别
        details: 详细信息
    """
    logger = logging.getLogger("system_events")
    
    log_message = f"系统事件 - {event}"
    if details:
        log_message += f" - {details}"
    
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.log(log_level, log_message)


def log_performance_metrics(operation: str, duration: float, memory_usage: float = 0, cpu_usage: float = 0):
    """
    记录性能指标日志
    
    Args:
        operation: 操作名称
        duration: 执行时间
        memory_usage: 内存使用量
        cpu_usage: CPU使用率
    """
    logger = logging.getLogger("performance")
    
    log_message = f"性能指标 - {operation} - 耗时: {duration:.3f}秒"
    
    if memory_usage > 0:
        log_message += f" - 内存: {memory_usage:.2f}MB"
    
    if cpu_usage > 0:
        log_message += f" - CPU: {cpu_usage:.2f}%"
    
    logger.info(log_message)


def log_error_with_context(error: Exception, context: dict = None):
    """
    记录带上下文的错误日志
    
    Args:
        error: 异常对象
        context: 上下文信息
    """
    logger = logging.getLogger("errors")
    
    log_message = f"系统错误 - {type(error).__name__}: {str(error)}"
    
    if context:
        log_message += f" - 上下文: {context}"
    
    logger.error(log_message, exc_info=True)


class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self):
        """获取日志记录器"""
        return logging.getLogger(self.__class__.__name__)
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def log_error(self, message: str, exc_info: bool = False):
        """记录错误日志"""
        self.logger.error(message, exc_info=exc_info)
    
    def log_debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)


# 初始化主日志记录器
main_logger = setup_logger("starbucks_fingerprint")
main_logger.info("日志系统初始化完成")
