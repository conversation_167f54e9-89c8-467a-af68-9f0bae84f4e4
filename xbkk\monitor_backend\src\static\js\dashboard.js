// 星巴克风控绕过系统 - 监控后台JavaScript

// 全局变量
let currentPage = 'overview';
let charts = {};
let websocket = null;
let refreshInterval = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuthentication();
    
    // 初始化页面
    initializePage();
    
    // 建立WebSocket连接
    connectWebSocket();
    
    // 开始定时刷新
    startAutoRefresh();
});

// 检查用户认证状态
function checkAuthentication() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 验证token有效性
    fetch('/api/auth/verify', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    }).then(response => {
        if (!response.ok) {
            localStorage.removeItem('access_token');
            window.location.href = '/login';
        }
    }).catch(error => {
        console.error('认证验证失败:', error);
        localStorage.removeItem('access_token');
        window.location.href = '/login';
    });
}

// 初始化页面
function initializePage() {
    // 初始化图表
    initializeCharts();
    
    // 加载初始数据
    loadOverviewData();
    
    // 绑定事件监听器
    bindEventListeners();
}

// 初始化图表
function initializeCharts() {
    // 请求趋势图表
    const requestTrendCtx = document.getElementById('requestTrendChart');
    if (requestTrendCtx) {
        charts.requestTrend = new Chart(requestTrendCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '请求数量',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // 请求类型分布图表
    const requestTypeCtx = document.getElementById('requestTypeChart');
    if (requestTypeCtx) {
        charts.requestType = new Chart(requestTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['正常请求', '可疑请求', '错误请求'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        '#27ae60',
                        '#f39c12',
                        '#e74c3c'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 图表时间段控制按钮
    document.querySelectorAll('.chart-controls button').forEach(button => {
        button.addEventListener('click', function() {
            // 移除其他按钮的active类
            this.parentElement.querySelectorAll('button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 更新图表数据
            const period = this.dataset.period;
            updateRequestTrendChart(period);
        });
    });
}

// 页面切换
function showPage(pageName) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.remove('active');
    });
    
    // 移除所有菜单项的active类
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageName + '-page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 激活对应菜单项
    const targetMenuItem = document.querySelector(`[data-page="${pageName}"]`);
    if (targetMenuItem) {
        targetMenuItem.classList.add('active');
    }
    
    // 更新当前页面
    currentPage = pageName;
    
    // 根据页面加载对应数据
    loadPageData(pageName);
}

// 加载页面数据
function loadPageData(pageName) {
    switch(pageName) {
        case 'overview':
            loadOverviewData();
            break;
        case 'logs':
            loadLogsData();
            break;
        case 'customers':
            loadCustomersData();
            break;
        case 'security':
            loadSecurityData();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
        case 'settings':
            loadSettingsData();
            break;
    }
}

// 加载概览数据
function loadOverviewData() {
    // 加载统计数据
    loadStatsData();
    
    // 加载系统状态
    loadSystemStatus();
    
    // 加载最近活动
    loadRecentActivity();
    
    // 更新图表
    updateRequestTrendChart('1h');
    updateRequestTypeChart();
}

// 加载统计数据
function loadStatsData() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/stats/customers', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新统计卡片
            document.getElementById('totalRequests').textContent = data.total_requests || 0;
            document.getElementById('activeCustomers').textContent = data.active_customers || 0;
            document.getElementById('suspiciousRequests').textContent = data.suspicious_requests || 0;
            document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'ms';
        }
    })
    .catch(error => {
        console.error('加载统计数据失败:', error);
    });
}

// 加载系统状态
function loadSystemStatus() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/system/status', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新CPU使用率
            const cpuUsage = data.cpu_usage || 0;
            document.getElementById('cpuUsage').style.width = cpuUsage + '%';
            document.getElementById('cpuValue').textContent = cpuUsage.toFixed(1) + '%';
            
            // 更新内存使用率
            const memoryUsage = data.memory_usage || 0;
            document.getElementById('memoryUsage').style.width = memoryUsage + '%';
            document.getElementById('memoryValue').textContent = memoryUsage.toFixed(1) + '%';
            
            // 更新磁盘使用率
            const diskUsage = data.disk_usage || 0;
            document.getElementById('diskUsage').style.width = diskUsage + '%';
            document.getElementById('diskValue').textContent = diskUsage.toFixed(1) + '%';
        }
    })
    .catch(error => {
        console.error('加载系统状态失败:', error);
    });
}

// 加载最近活动
function loadRecentActivity() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/logs?limit=10', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.logs) {
            const activityContainer = document.getElementById('recentActivity');
            activityContainer.innerHTML = '';
            
            data.logs.forEach(log => {
                const activityItem = createActivityItem(log);
                activityContainer.appendChild(activityItem);
            });
        }
    })
    .catch(error => {
        console.error('加载最近活动失败:', error);
    });
}

// 创建活动项目元素
function createActivityItem(log) {
    const item = document.createElement('div');
    item.className = 'activity-item';
    
    const statusClass = log.is_suspicious ? 'text-warning' : 
                       (log.response_status >= 400 ? 'text-danger' : 'text-success');
    
    item.innerHTML = `
        <div class="activity-icon ${statusClass}">
            <i class="bi bi-${log.is_suspicious ? 'exclamation-triangle' : 
                              (log.response_status >= 400 ? 'x-circle' : 'check-circle')}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${log.api_endpoint}</div>
            <div class="activity-meta">
                <span class="activity-customer">${log.customer_id}</span>
                <span class="activity-time">${formatTime(log.timestamp)}</span>
                <span class="activity-status ${statusClass}">${log.response_status}</span>
            </div>
        </div>
    `;
    
    return item;
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 小于1分钟
        return '刚刚';
    } else if (diff < 3600000) { // 小于1小时
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 小于1天
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString();
    }
}

// 更新请求趋势图表
function updateRequestTrendChart(period) {
    // 这里应该根据period参数获取对应时间段的数据
    // 暂时使用模拟数据
    const mockData = generateMockTrendData(period);
    
    if (charts.requestTrend) {
        charts.requestTrend.data.labels = mockData.labels;
        charts.requestTrend.data.datasets[0].data = mockData.data;
        charts.requestTrend.update();
    }
}

// 更新请求类型图表
function updateRequestTypeChart() {
    // 这里应该获取真实的请求类型分布数据
    // 暂时使用模拟数据
    const mockData = [85, 12, 3]; // 正常、可疑、错误
    
    if (charts.requestType) {
        charts.requestType.data.datasets[0].data = mockData;
        charts.requestType.update();
    }
}

// 生成模拟趋势数据
function generateMockTrendData(period) {
    const now = new Date();
    const labels = [];
    const data = [];
    
    let points, interval;
    switch(period) {
        case '1h':
            points = 12;
            interval = 5 * 60 * 1000; // 5分钟
            break;
        case '6h':
            points = 24;
            interval = 15 * 60 * 1000; // 15分钟
            break;
        case '24h':
            points = 24;
            interval = 60 * 60 * 1000; // 1小时
            break;
        default:
            points = 12;
            interval = 5 * 60 * 1000;
    }
    
    for (let i = points - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval);
        labels.push(time.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        }));
        data.push(Math.floor(Math.random() * 100) + 20);
    }
    
    return { labels, data };
}

// WebSocket连接
function connectWebSocket() {
    // 这里应该建立WebSocket连接来接收实时数据
    // 暂时跳过WebSocket实现
    console.log('WebSocket连接功能待实现');
}

// 开始自动刷新
function startAutoRefresh() {
    // 每30秒刷新一次数据
    refreshInterval = setInterval(() => {
        if (currentPage === 'overview') {
            loadOverviewData();
        }
    }, 30000);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// 刷新活动
function refreshActivity() {
    loadRecentActivity();
}

// 显示系统信息
function showSystemInfo() {
    // 更新系统运行时间
    updateSystemUptime();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
    modal.show();
}

// 更新系统运行时间
function updateSystemUptime() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/system/status', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('systemUptime').textContent = data.uptime || '未知';
            document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
        }
    })
    .catch(error => {
        console.error('获取系统信息失败:', error);
        document.getElementById('systemUptime').textContent = '获取失败';
        document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
    });
}

// 用户登出
function logout() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(() => {
        localStorage.removeItem('access_token');
        localStorage.removeItem('remember_login');
        window.location.href = '/login';
    })
    .catch(error => {
        console.error('登出失败:', error);
        // 即使登出请求失败，也清除本地token
        localStorage.removeItem('access_token');
        localStorage.removeItem('remember_login');
        window.location.href = '/login';
    });
}

// 其他页面的数据加载函数（暂时为空，后续实现）
function loadLogsData() {
    console.log('加载日志数据');
}

function loadCustomersData() {
    console.log('加载客户数据');
}

function loadSecurityData() {
    console.log('加载安全数据');
}

function loadAnalyticsData() {
    console.log('加载分析数据');
}

function loadSettingsData() {
    console.log('加载设置数据');
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
    if (websocket) {
        websocket.close();
    }
});
