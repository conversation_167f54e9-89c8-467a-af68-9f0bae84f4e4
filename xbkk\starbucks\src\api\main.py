#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI主应用
作者：YINGAshadow
创建时间：2025-7-29
功能：提供HTTP API接口
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ..config.settings import settings
from ..core.device_manager import device_manager, DeviceStatus
from ..core.f5_shape_generator import f5_generator
from ..utils.logger import setup_logger
from ..utils.bypass_tester import bypass_tester
from ..utils.auth import get_current_user, get_admin_user, check_rate_limit, get_client_ip, verify_api_key_header
from ..utils.monitor import system_monitor


# 设置日志
logger = setup_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求模型
class FingerprintRequest(BaseModel):
    """指纹生成请求"""
    device_count: int = 1
    force_regenerate: bool = True


class TestRequest(BaseModel):
    """风控测试请求"""
    device_index: Optional[int] = None
    test_endpoint: Optional[str] = None
    concurrent_count: int = 1


class DeviceOperationRequest(BaseModel):
    """设备操作请求"""
    device_index: int
    operation: str  # "block", "regenerate", "release"
    reason: Optional[str] = ""


class CustomerTestRequest(BaseModel):
    """客户测试服务请求模型"""
    target_url: str
    test_config: Dict = {
        "device_count": 30,
        "method": "GET",
        "headers": {},
        "data": {},
        "concurrent_limit": 10,
        "delay_between_requests": 0.1
    }


class RealVerificationRequest(BaseModel):
    """真实验证请求 - 客户可以用自己的端点验证"""
    target_url: str
    test_method: str = "GET"
    expected_block_status: int = 469  # 客户期望的风控状态码


# 响应模型
class ApiResponse(BaseModel):
    """API响应基类"""
    success: bool
    message: str
    data: Optional[Dict] = None
    timestamp: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


class FingerprintResponse(ApiResponse):
    """指纹响应"""
    fingerprints: Optional[List[Dict]] = None


class TestResponse(ApiResponse):
    """测试响应"""
    test_results: Optional[List[Dict]] = None


# 全局变量
concurrent_requests = 0
max_concurrent = settings.MAX_CONCURRENT_REQUESTS


@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """请求中间件：限流、监控、日志"""
    global concurrent_requests

    start_time = time.time()
    client_ip = get_client_ip(request)

    # 检查并发限制
    if concurrent_requests >= max_concurrent:
        return JSONResponse(
            status_code=429,
            content={"success": False, "message": "服务器繁忙，请稍后重试"}
        )

    # 检查频率限制（对于非认证端点）
    if not request.url.path.startswith("/docs") and not request.url.path.startswith("/redoc"):
        try:
            await check_rate_limit(request)
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"success": False, "message": e.detail},
                headers=e.headers
            )

    # 读取请求体（用于监控）
    request_body = ""
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            body = await request.body()
            request_body = body.decode('utf-8') if body else ""
        except:
            request_body = ""

    concurrent_requests += 1
    try:
        response = await call_next(request)

        # 记录API指标
        response_time = time.time() - start_time
        success = response.status_code < 400
        system_monitor.metrics_collector.record_api_request(success, response_time)

        # 获取客户ID和用户信息
        customer_id = "system"
        user_type = "internal"

        # 检查是否是客户API
        if "/api/bypass/" in str(request.url.path):
            user_type = "customer"
            try:
                # 从X-API-Key头获取客户API密钥
                api_key = request.headers.get("X-API-Key", "")
                if api_key:
                    customer_id = f"customer_{api_key[:8]}"
                else:
                    customer_id = "unknown_customer"
            except:
                customer_id = "unknown_customer"
        else:
            # 检查是否有认证信息
            try:
                auth_header = request.headers.get("Authorization", "")
                if auth_header.startswith("Bearer "):
                    token = auth_header[7:]
                    customer_id = f"admin_{token[:8]}"
                    user_type = "admin"
                else:
                    customer_id = f"anonymous_{client_ip.replace('.', '_')}"
                    user_type = "anonymous"
            except:
                customer_id = f"anonymous_{client_ip.replace('.', '_')}"
                user_type = "anonymous"

        # 读取响应体（用于监控）
        response_body = ""
        try:
            if hasattr(response, 'body'):
                response_body = str(response.body)[:1000]  # 限制长度
        except:
            response_body = ""

        # 记录到监控后台（所有API请求）
        # 排除健康检查和静态资源
        if not any(path in str(request.url.path) for path in ["/health", "/favicon.ico", "/static/"]):
            try:
                await system_monitor.metrics_collector.record_all_requests(
                    client_ip=client_ip,
                    customer_id=customer_id,
                    user_type=user_type,
                    endpoint=str(request.url.path),
                    method=request.method,
                    headers=dict(request.headers),
                    request_body=request_body[:1000],  # 限制长度
                    response_status=response.status_code,
                    response_body=response_body,
                    response_time=response_time,
                    user_agent=request.headers.get("User-Agent", "")
                )
            except Exception as e:
                logger.error(f"发送监控日志失败: {str(e)}")

        # 记录访问日志
        logger.info(f"API请求 - IP: {client_ip}, 路径: {request.url.path}, "
                   f"方法: {request.method}, 状态: {response.status_code}, "
                   f"响应时间: {response_time:.3f}s, 客户: {customer_id}")

        return response
    finally:
        concurrent_requests -= 1


@app.get("/", response_model=ApiResponse)
async def root():
    """根路径"""
    return ApiResponse(
        success=True,
        message="星巴克设备指纹风控绕过系统运行正常",
        data={
            "version": settings.API_VERSION,
            "max_devices": settings.MAX_DEVICES,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    )


@app.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    try:
        stats = device_manager.get_device_stats()
        return ApiResponse(
            success=True,
            message="系统健康状态良好",
            data=stats
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@app.post("/api/v1/fingerprint/generate", response_model=FingerprintResponse)
async def generate_fingerprint(request: FingerprintRequest):
    """生成设备指纹"""
    try:
        if request.device_count <= 0 or request.device_count > settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400, 
                detail=f"设备数量必须在1-{settings.MAX_DEVICES}之间"
            )
        
        fingerprints = []

        if request.force_regenerate:
            # 强制重新生成 - 使用增强的动态设备索引确保唯一性
            import time
            import random
            import uuid

            # 使用毫秒级时间戳提高唯一性
            base_timestamp = int(time.time() * 1000)
            request_id = str(uuid.uuid4())[:8]  # 请求唯一标识

            for i in range(request.device_count):
                # 增强的动态设备索引生成
                # 组合：毫秒时间戳 + 序列号 + 大范围随机数 + 请求ID哈希
                time_component = base_timestamp + i
                random_component = random.randint(0, 99999)
                request_hash = hash(request_id + str(i)) % 10000

                dynamic_index = (time_component + random_component + request_hash) % 1000000
                fingerprint = f5_generator.generate_fingerprint(dynamic_index)
                fingerprints.append(fingerprint)
                logger.info(f"生成增强动态设备指纹，索引: {dynamic_index}, 请求ID: {request_id}")
        else:
            # 使用现有设备
            for i in range(request.device_count):
                device = device_manager.devices.get(i)
                if device:
                    fingerprints.append(device.fingerprint)
                else:
                    # 如果设备不存在，生成新的
                    fingerprint = f5_generator.generate_fingerprint(i)
                    fingerprints.append(fingerprint)
        
        logger.info(f"成功生成 {len(fingerprints)} 个设备指纹")
        
        return FingerprintResponse(
            success=True,
            message=f"成功生成 {len(fingerprints)} 个设备指纹",
            fingerprints=fingerprints
        )
        
    except Exception as e:
        logger.error(f"生成指纹失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成指纹失败: {str(e)}")


@app.get("/api/v1/fingerprint/{device_index}", response_model=FingerprintResponse)
async def get_device_fingerprint(device_index: int, force_regenerate: bool = True):
    """获取指定设备的指纹 - 默认每次重新生成确保唯一性"""
    try:
        if device_index < 0 or device_index >= settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400,
                detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
            )

        if force_regenerate:
            # 每次都生成新的指纹，确保唯一性
            import time
            import random
            dynamic_index = (int(time.time()) + device_index + random.randint(0, 9999)) % 100000
            fresh_fingerprint = f5_generator.generate_fingerprint(dynamic_index)
            logger.info(f"为设备 {device_index} 生成新指纹，动态索引: {dynamic_index}")

            return FingerprintResponse(
                success=True,
                message=f"生成设备 {device_index} 新指纹成功",
                fingerprints=[fresh_fingerprint]
            )
        else:
            # 使用缓存的指纹
            device = device_manager.devices.get(device_index)
            if not device:
                raise HTTPException(status_code=404, detail=f"设备 {device_index} 不存在")

            return FingerprintResponse(
                success=True,
                message=f"获取设备 {device_index} 缓存指纹成功",
                fingerprints=[device.fingerprint]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备指纹失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备指纹失败: {str(e)}")


@app.get("/api/v1/fingerprint/single", response_model=FingerprintResponse)
async def get_single_fingerprint():
    """获取单个无限制指纹 - 真正的一键新机效果

    每次调用都返回全新的虚拟设备指纹
    支持无限连续调用，每次都完全不同
    不依赖固定设备池，真正模拟一键新机
    """
    try:
        # 生成无限制的单个指纹
        fresh_fingerprint = f5_generator.generate_unlimited_fingerprint()

        logger.info("成功生成无限制单个指纹")

        return FingerprintResponse(
            success=True,
            message="成功生成单个指纹",
            fingerprints=[fresh_fingerprint]
        )

    except Exception as e:
        logger.error(f"生成单个指纹失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成单个指纹失败: {str(e)}")


@app.post("/api/v1/test/bypass", response_model=TestResponse)
async def test_bypass(request: TestRequest, background_tasks: BackgroundTasks):
    """测试风控绕过效果"""
    try:
        # 使用真实的星巴克API端点
        test_endpoint = request.test_endpoint or settings.DEFAULT_TEST_ENDPOINT
        
        if request.device_index is not None:
            # 测试指定设备
            if request.device_index < 0 or request.device_index >= settings.MAX_DEVICES:
                raise HTTPException(
                    status_code=400,
                    detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
                )
            
            device = device_manager.devices.get(request.device_index)
            if not device:
                raise HTTPException(status_code=404, detail=f"设备 {request.device_index} 不存在")
            
            result = await bypass_tester.test_single_device(device, test_endpoint, "GET")
            test_results = [result]
            
        else:
            # 并发测试多个设备
            concurrent_count = min(request.concurrent_count, settings.MAX_DEVICES)
            test_results = await bypass_tester.test_concurrent_devices(
                concurrent_count, test_endpoint
            )
        
        # 后台任务：更新设备状态
        background_tasks.add_task(
            bypass_tester.update_device_status_from_results, test_results
        )
        
        success_count = sum(1 for result in test_results if result.get("success", False))
        success_rate = success_count / len(test_results) if test_results else 0
        
        logger.info(f"风控测试完成，成功率: {success_rate:.2%}")
        
        return TestResponse(
            success=True,
            message=f"测试完成，成功率: {success_rate:.2%}",
            test_results=test_results,
            data={
                "total_tests": len(test_results),
                "success_count": success_count,
                "success_rate": success_rate
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"风控测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"风控测试失败: {str(e)}")


@app.get("/api/v1/devices", response_model=ApiResponse)
async def get_devices():
    """获取设备列表"""
    try:
        device_list = device_manager.get_device_list()
        stats = device_manager.get_device_stats()
        
        return ApiResponse(
            success=True,
            message="获取设备列表成功",
            data={
                "devices": device_list,
                "statistics": stats
            }
        )
        
    except Exception as e:
        logger.error(f"获取设备列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")


@app.post("/api/v1/devices/operation", response_model=ApiResponse)
async def device_operation(request: DeviceOperationRequest):
    """设备操作"""
    try:
        device_index = request.device_index
        operation = request.operation.lower()
        
        if device_index < 0 or device_index >= settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400,
                detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
            )
        
        if operation == "block":
            device_manager.block_device(device_index, request.reason)
            message = f"设备 {device_index} 已被封禁"
            
        elif operation == "regenerate":
            success = device_manager.regenerate_device(device_index)
            if not success:
                raise HTTPException(status_code=500, detail=f"重新生成设备 {device_index} 失败")
            message = f"设备 {device_index} 指纹已重新生成"
            
        elif operation == "release":
            device_manager.release_device(device_index, True)
            message = f"设备 {device_index} 已释放"
            
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {operation}")
        
        logger.info(f"设备操作成功: {message}")
        
        return ApiResponse(
            success=True,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设备操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设备操作失败: {str(e)}")


@app.post("/api/v1/devices/cleanup", response_model=ApiResponse)
async def cleanup_devices(current_user: str = Depends(get_admin_user)):
    """清理被封设备（需要管理员权限）"""
    try:
        device_manager.cleanup_blocked_devices()

        logger.info(f"管理员 {current_user} 执行清理被封设备操作")

        return ApiResponse(
            success=True,
            message="被封设备清理完成",
            data={"operator": current_user}
        )

    except Exception as e:
        logger.error(f"清理设备失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理设备失败: {str(e)}")


@app.get("/api/v1/system/status", response_model=ApiResponse)
async def get_system_status(current_user: str = Depends(get_current_user)):
    """获取系统状态（需要认证）"""
    try:
        # 获取监控状态
        monitor_status = system_monitor.get_status()

        # 获取设备统计
        device_stats = device_manager.get_device_stats()

        # 获取活跃告警
        active_alerts = system_monitor.alert_manager.get_active_alerts()

        return ApiResponse(
            success=True,
            message="获取系统状态成功",
            data={
                "monitor_status": monitor_status,
                "device_stats": device_stats,
                "active_alerts": len(active_alerts),
                "alerts": [
                    {
                        "id": alert.alert_id,
                        "type": alert.alert_type,
                        "level": alert.level,
                        "message": alert.message,
                        "timestamp": alert.timestamp
                    } for alert in active_alerts[:10]  # 只显示前10个告警
                ]
            }
        )

    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


# 客户服务接口
@app.post("/api/bypass/test-service", response_model=ApiResponse)
async def customer_bypass_test_service(
    request: CustomerTestRequest,
    customer_id: str = Depends(verify_api_key_header)
):
    """
    客户风控绕过测试服务
    为客户提供的风控绕过能力测试接口
    """
    try:
        logger.info(f"客户 {customer_id} 请求测试: {request.target_url}")

        # 获取测试配置
        config = request.test_config
        device_count = config.get("device_count", 30)
        method = config.get("method", "GET")
        headers = config.get("headers", {})
        data = config.get("data", {})
        concurrent_limit = config.get("concurrent_limit", 10)
        delay = config.get("delay_between_requests", 0.1)

        # 执行并发绕过测试
        test_results = []
        successful_bypasses = 0
        total_response_time = 0

        # 分批并发测试
        for batch_start in range(0, device_count, concurrent_limit):
            batch_end = min(batch_start + concurrent_limit, device_count)
            batch_tasks = []

            for device_index in range(batch_start, batch_end):
                # 获取设备对象
                device = device_manager.devices.get(device_index)
                if not device:
                    # 如果设备不存在，跳过
                    test_results.append({
                        "device_index": device_index,
                        "success": False,
                        "error": f"设备 {device_index} 不存在"
                    })
                    continue

                # 使用正确的方法签名调用，传递HTTP方法和数据
                task = bypass_tester.test_single_device(
                    device=device,
                    test_endpoint=request.target_url,
                    method=method,
                    headers=headers,
                    data=data
                )
                batch_tasks.append(task)

            # 执行当前批次
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            for i, result in enumerate(batch_results):
                device_index = batch_start + i
                if isinstance(result, Exception):
                    test_results.append({
                        "device_index": device_index,
                        "success": False,
                        "error": str(result)
                    })
                else:
                    test_results.append(result)
                    # 使用正确的字段名
                    if result.get("success", False) or result.get("bypass_confirmed", False):
                        successful_bypasses += 1
                    total_response_time += result.get("response_time", 0)

            # 批次间延迟
            if batch_end < device_count:
                await asyncio.sleep(delay)

        # 计算统计数据
        success_rate = successful_bypasses / device_count if device_count > 0 else 0
        avg_response_time = total_response_time / device_count if device_count > 0 else 0
        blocked_requests = device_count - successful_bypasses

        # 计算绕过评分
        bypass_score = min(success_rate + 0.1, 1.0)  # 基础评分加成

        # 效果评估
        if bypass_score >= 0.8:
            effectiveness = "优秀"
            recommendation = "该接口风控可以被有效绕过，建议使用我们的服务"
        elif bypass_score >= 0.6:
            effectiveness = "良好"
            recommendation = "该接口风控可以被部分绕过，有一定效果"
        else:
            effectiveness = "一般"
            recommendation = "该接口风控较强，绕过效果有限"

        return ApiResponse(
            success=True,
            message="风控绕过测试完成",
            data={
                "service_name": "星巴克F5 Shape风控绕过测试服务",
                "test_result": {
                    "target_url": request.target_url,
                    "bypass_success_rate": round(success_rate, 3),
                    "bypass_score": round(bypass_score, 2),
                    "total_tests": device_count,
                    "successful_bypasses": successful_bypasses,
                    "blocked_requests": blocked_requests,
                    "average_response_time": round(avg_response_time, 3),
                    "effectiveness": effectiveness,
                    "recommendation": recommendation
                },
                "service_info": {
                    "technology": "F5 Shape指纹技术",
                    "sample_base": "435个真实样本",
                    "concurrent_devices": device_count,
                    "test_method": method,
                    "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "technical_details": {
                    "fingerprint_technology": "F5 Shape设备指纹",
                    "bypass_algorithm": "多层编码绕过算法",
                    "device_simulation": "真实设备特征模拟",
                    "success_criteria": "HTTP状态码200且无风控拦截标识"
                }
            }
        )

    except Exception as e:
        logger.error(f"客户测试服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试服务失败: {str(e)}")


@app.post("/api/v1/verify/real-test", response_model=ApiResponse)
async def real_bypass_verification(
    request: RealVerificationRequest,
    customer_id: str = Depends(verify_api_key_header)
):
    """
    真实绕过验证接口
    客户可以用自己的测试端点验证系统的真实绕过能力
    特别针对469状态码等风控响应进行验证
    """
    try:
        logger.info(f"客户 {customer_id} 请求真实验证: {request.target_url}")

        # 1. 基准测试 - 无指纹请求
        logger.info("执行基准测试（无指纹）...")
        baseline_result = await perform_baseline_test(request.target_url, request.test_method)

        # 2. 指纹测试 - 使用生成的指纹
        logger.info("执行指纹测试...")
        import time
        import random
        dynamic_index = (int(time.time()) + random.randint(0, 9999)) % 100000
        fingerprint = f5_generator.generate_fingerprint(dynamic_index)
        fingerprint_result = await perform_fingerprint_test(
            request.target_url, request.test_method, fingerprint
        )

        # 3. 对比分析
        comparison = compare_test_results(baseline_result, fingerprint_result, request.expected_block_status)

        return ApiResponse(
            success=True,
            message="真实验证完成",
            data={
                "verification_summary": {
                    "target_url": request.target_url,
                    "test_method": request.test_method,
                    "expected_block_status": request.expected_block_status,
                    "real_bypass_confirmed": comparison["bypass_confirmed"],
                    "credibility_score": comparison["credibility_score"]
                },
                "baseline_test": baseline_result,
                "fingerprint_test": fingerprint_result,
                "comparison_analysis": comparison,
                "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        )

    except Exception as e:
        logger.error(f"真实验证失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"真实验证失败: {str(e)}")


async def perform_baseline_test(target_url: str, method: str = "GET") -> Dict:
    """执行基准测试（无指纹）"""
    try:
        import httpx
        async with httpx.AsyncClient(timeout=10.0) as client:
            if method.upper() == "GET":
                response = await client.get(target_url)
            else:
                response = await client.post(target_url)

            return {
                "status_code": response.status_code,
                "response_size": len(response.content),
                "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                "headers": dict(response.headers),
                "is_blocked": response.status_code in [403, 429, 469, 503],
                "content_preview": response.text[:200] if response.text else ""
            }
    except Exception as e:
        return {
            "error": str(e),
            "status_code": 0,
            "is_blocked": True
        }


async def perform_fingerprint_test(target_url: str, method: str, fingerprint: Dict) -> Dict:
    """执行指纹测试"""
    try:
        import httpx

        # 构建指纹头部
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive"
        }

        # 添加F5 Shape指纹头部
        for key, value in fingerprint.items():
            if key.startswith("X-XHPAcPXq-") or key == "x-device-id":
                headers[key] = value

        async with httpx.AsyncClient(timeout=10.0) as client:
            if method.upper() == "GET":
                response = await client.get(target_url, headers=headers)
            else:
                response = await client.post(target_url, headers=headers)

            return {
                "status_code": response.status_code,
                "response_size": len(response.content),
                "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                "headers": dict(response.headers),
                "is_blocked": response.status_code in [403, 429, 469, 503],
                "content_preview": response.text[:200] if response.text else "",
                "fingerprint_used": fingerprint
            }
    except Exception as e:
        return {
            "error": str(e),
            "status_code": 0,
            "is_blocked": True
        }


def compare_test_results(baseline: Dict, fingerprint: Dict, expected_block_status: int) -> Dict:
    """对比测试结果"""
    comparison = {
        "bypass_confirmed": False,
        "credibility_score": 0.0,
        "evidence": [],
        "analysis": {}
    }

    try:
        baseline_blocked = baseline.get("is_blocked", True)
        fingerprint_blocked = fingerprint.get("is_blocked", True)

        baseline_status = baseline.get("status_code", 0)
        fingerprint_status = fingerprint.get("status_code", 0)

        # 分析1: 状态码对比
        if baseline_status == expected_block_status and fingerprint_status != expected_block_status:
            comparison["credibility_score"] += 0.4
            comparison["evidence"].append(f"基准测试返回预期的风控状态码{expected_block_status}，指纹测试成功绕过")

        # 分析2: 响应大小对比
        baseline_size = baseline.get("response_size", 0)
        fingerprint_size = fingerprint.get("response_size", 0)
        if fingerprint_size > baseline_size * 2:
            comparison["credibility_score"] += 0.3
            comparison["evidence"].append("指纹测试获得了更完整的响应内容")

        # 分析3: 响应时间对比
        baseline_time = baseline.get("response_time", 0)
        fingerprint_time = fingerprint.get("response_time", 0)
        if 0 < fingerprint_time < baseline_time * 2:
            comparison["credibility_score"] += 0.2
            comparison["evidence"].append("指纹测试响应时间正常")

        # 分析4: 错误信息对比
        if not fingerprint_blocked and baseline_blocked:
            comparison["credibility_score"] += 0.1
            comparison["evidence"].append("指纹测试未被标记为拦截")

        # 最终判断
        comparison["bypass_confirmed"] = comparison["credibility_score"] >= 0.6

        comparison["analysis"] = {
            "baseline_status": baseline_status,
            "fingerprint_status": fingerprint_status,
            "baseline_size": baseline_size,
            "fingerprint_size": fingerprint_size,
            "status_improvement": fingerprint_status == 200 and baseline_status != 200,
            "size_improvement": fingerprint_size > baseline_size
        }

    except Exception as e:
        comparison["error"] = str(e)

    return comparison


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("星巴克设备指纹风控绕过系统启动中...")

    # 执行设备健康检查
    await device_manager.health_check()

    # 启动系统监控
    await system_monitor.start_monitoring(interval=60)

    logger.info(f"系统启动完成，监听端口: {settings.PORT}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("星巴克设备指纹风控绕过系统正在关闭...")

    # 停止系统监控
    await system_monitor.stop_monitoring()


if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
