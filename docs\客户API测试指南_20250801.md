# 客户API测试指南

**系统名称**: 星巴克设备指纹风控绕过系统  
**服务地址**: http://你的服务器IP:8094  
**监控地址**: http://你的服务器IP:9094  
**更新时间**: 2025-8-1  

## 快速开始

### 1. 基础信息
- **主服务端口**: 8094 (Nginx代理)
- **监控端口**: 9094 (Nginx代理)
- **认证方式**: API Key认证
- **响应格式**: JSON

### 2. 默认API密钥
```
客户测试密钥: test-api-key-2025
管理员密钥: admin-api-key-2025
```

---

## 核心API接口

### 1. 系统健康检查
**接口**: `GET /health`  
**说明**: 检查系统运行状态  
**认证**: 无需认证  

```bash
curl -X GET "http://你的服务器IP:8094/health"
```

**响应示例**:
```json
{
  "success": true,
  "message": "系统健康状态良好",
  "data": {
    "total_devices": 30,
    "active_devices": 30,
    "success_rate": 0.95
  },
  "timestamp": "2025-08-01 10:30:00"
}
```

### 2. 获取设备指纹
**接口**: `POST /api/v1/fingerprints`  
**说明**: 获取F5 Shape绕过指纹  
**认证**: 需要API Key  

```bash
curl -X POST "http://你的服务器IP:8094/api/v1/fingerprints" \
  -H "X-API-Key: test-api-key-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "device_count": 5,
    "target_url": "https://app.starbucks.com.cn/api/test"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "指纹生成成功",
  "fingerprints": [
    {
      "device_index": 0,
      "headers": {
        "x-device-id": "ABC123...",
        "X-XHPAcPXq-z": "...",
        "X-XHPAcPXq-g": "...",
        "Authorization": "Bearer ..."
      }
    }
  ],
  "timestamp": "2025-08-01 10:30:00"
}
```

### 3. 测试绕过效果
**接口**: `POST /api/v1/test/bypass`  
**说明**: 测试指定接口的绕过效果  
**认证**: 需要API Key  

```bash
curl -X POST "http://你的服务器IP:8094/api/v1/test/bypass" \
  -H "X-API-Key: test-api-key-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://app.starbucks.com.cn/api/test",
    "device_count": 10,
    "test_duration": 60
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "绕过测试完成",
  "test_results": [
    {
      "device_index": 0,
      "success": true,
      "response_time": 0.234,
      "status_code": 200,
      "bypass_score": 0.95
    }
  ],
  "summary": {
    "total_requests": 10,
    "successful_bypasses": 9,
    "success_rate": 0.9,
    "avg_response_time": 0.245,
    "effectiveness": "优秀",
    "recommendation": "该接口风控可以被有效绕过，建议使用我们的服务"
  },
  "timestamp": "2025-08-01 10:30:00"
}
```

### 4. 获取设备列表
**接口**: `GET /api/v1/devices`  
**说明**: 获取可用设备列表  
**认证**: 需要API Key  

```bash
curl -X GET "http://你的服务器IP:8094/api/v1/devices" \
  -H "X-API-Key: test-api-key-2025"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取设备列表成功",
  "data": {
    "devices": [
      {
        "index": 0,
        "status": "active",
        "last_used": "2025-08-01 10:25:00",
        "success_rate": 0.95
      }
    ],
    "total_count": 30,
    "active_count": 30
  },
  "timestamp": "2025-08-01 10:30:00"
}
```

---

## 管理员API接口

### 1. 管理员登录
**接口**: `POST /api/v1/admin/login`  
**说明**: 管理员认证登录  

```bash
curl -X POST "http://你的服务器IP:8094/api/v1/admin/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123456"
  }'
```

### 2. 系统状态
**接口**: `GET /api/v1/system/status`  
**说明**: 获取详细系统状态  
**认证**: 需要管理员Token  

```bash
curl -X GET "http://你的服务器IP:8094/api/v1/system/status" \
  -H "Authorization: Bearer 你的管理员Token"
```

---

## 监控后台接口

### 1. 监控登录
**接口**: `POST /api/login`  
**说明**: 监控后台登录  

```bash
curl -X POST "http://你的服务器IP:9094/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "monitor",
    "password": "monitor123456"
  }'
```

### 2. 客户使用统计
**接口**: `GET /api/customer-stats`  
**说明**: 获取客户使用统计  
**认证**: 需要监控Token  

```bash
curl -X GET "http://你的服务器IP:9094/api/customer-stats" \
  -H "Authorization: Bearer 你的监控Token"
```

### 3. 系统监控数据
**接口**: `GET /api/system-monitor`  
**说明**: 获取系统监控数据  
**认证**: 需要监控Token  

```bash
curl -X GET "http://你的服务器IP:9094/api/system-monitor" \
  -H "Authorization: Bearer 你的监控Token"
```

---

## 客户集成示例

### Python示例
```python
import requests
import json

class StarbucksBypassClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def get_fingerprints(self, device_count=5, target_url=None):
        """获取设备指纹"""
        url = f"{self.base_url}/api/v1/fingerprints"
        data = {
            'device_count': device_count,
            'target_url': target_url
        }
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()
    
    def test_bypass(self, target_url, device_count=10):
        """测试绕过效果"""
        url = f"{self.base_url}/api/v1/test/bypass"
        data = {
            'target_url': target_url,
            'device_count': device_count,
            'test_duration': 60
        }
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()

# 使用示例
client = StarbucksBypassClient(
    base_url="http://你的服务器IP:8094",
    api_key="test-api-key-2025"
)

# 获取指纹
fingerprints = client.get_fingerprints(device_count=5)
print("指纹获取结果:", fingerprints)

# 测试绕过
bypass_result = client.test_bypass("https://app.starbucks.com.cn/api/test")
print("绕过测试结果:", bypass_result)
```

### JavaScript示例
```javascript
class StarbucksBypassClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }
    
    async getFingerprints(deviceCount = 5, targetUrl = null) {
        const response = await fetch(`${this.baseUrl}/api/v1/fingerprints`, {
            method: 'POST',
            headers: {
                'X-API-Key': this.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                device_count: deviceCount,
                target_url: targetUrl
            })
        });
        return await response.json();
    }
    
    async testBypass(targetUrl, deviceCount = 10) {
        const response = await fetch(`${this.baseUrl}/api/v1/test/bypass`, {
            method: 'POST',
            headers: {
                'X-API-Key': this.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                target_url: targetUrl,
                device_count: deviceCount,
                test_duration: 60
            })
        });
        return await response.json();
    }
}

// 使用示例
const client = new StarbucksBypassClient(
    "http://你的服务器IP:8094",
    "test-api-key-2025"
);

// 获取指纹
client.getFingerprints(5).then(result => {
    console.log("指纹获取结果:", result);
});

// 测试绕过
client.testBypass("https://app.starbucks.com.cn/api/test").then(result => {
    console.log("绕过测试结果:", result);
});
```

---

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | API Key无效 | 检查API Key是否正确 |
| 403 | 权限不足 | 使用正确的认证方式 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

---

## 技术支持

### 联系方式
- **技术支持**: 通过监控后台查看系统状态
- **API文档**: 访问 http://你的服务器IP:8094/docs
- **监控面板**: 访问 http://你的服务器IP:9094

### 常见问题
1. **Q**: 如何获取API Key？  
   **A**: 联系管理员分配专用API Key

2. **Q**: 支持哪些目标网站？  
   **A**: 支持所有使用F5 Shape风控的网站

3. **Q**: 绕过成功率如何？  
   **A**: 通常可达90%以上的绕过成功率

4. **Q**: 如何监控使用情况？  
   **A**: 通过监控后台实时查看使用统计
