<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星巴克风控绕过系统 - 监控后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-body">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check me-2"></i>
                星巴克风控绕过系统 - 监控后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showSystemInfo()">
                            <i class="bi bi-info-circle me-2"></i>系统信息
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h5>监控管理</h5>
        </div>
        <ul class="sidebar-menu">
            <li class="menu-item active" data-page="overview">
                <a href="#" onclick="showPage('overview')">
                    <i class="bi bi-speedometer2"></i>
                    <span>系统概览</span>
                </a>
            </li>
            <li class="menu-item" data-page="logs">
                <a href="#" onclick="showPage('logs')">
                    <i class="bi bi-journal-text"></i>
                    <span>实时日志</span>
                    <span class="badge bg-danger" id="newLogsBadge">0</span>
                </a>
            </li>
            <li class="menu-item" data-page="customers">
                <a href="#" onclick="showPage('customers')">
                    <i class="bi bi-people"></i>
                    <span>客户统计</span>
                </a>
            </li>
            <li class="menu-item" data-page="security">
                <a href="#" onclick="showPage('security')">
                    <i class="bi bi-shield-exclamation"></i>
                    <span>安全监控</span>
                    <span class="badge bg-warning" id="securityAlertsBadge">0</span>
                </a>
            </li>
            <li class="menu-item" data-page="analytics">
                <a href="#" onclick="showPage('analytics')">
                    <i class="bi bi-graph-up"></i>
                    <span>数据分析</span>
                </a>
            </li>
            <li class="menu-item" data-page="settings">
                <a href="#" onclick="showPage('settings')">
                    <i class="bi bi-gear"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 系统概览页面 -->
        <div id="overview-page" class="page-content active">
            <div class="page-header">
                <h2><i class="bi bi-speedometer2 me-2"></i>系统概览</h2>
                <p class="text-muted">实时监控系统运行状态和关键指标</p>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card bg-primary">
                        <div class="stats-icon">
                            <i class="bi bi-activity"></i>
                        </div>
                        <div class="stats-content">
                            <h3 id="totalRequests">0</h3>
                            <p>总请求数</p>
                            <small class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +12.5%
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card bg-success">
                        <div class="stats-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-content">
                            <h3 id="activeCustomers">0</h3>
                            <p>活跃客户</p>
                            <small class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +8.2%
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card bg-warning">
                        <div class="stats-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="stats-content">
                            <h3 id="suspiciousRequests">0</h3>
                            <p>可疑请求</p>
                            <small class="stats-change negative">
                                <i class="bi bi-arrow-down"></i> -3.1%
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card bg-info">
                        <div class="stats-icon">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="stats-content">
                            <h3 id="avgResponseTime">0ms</h3>
                            <p>平均响应时间</p>
                            <small class="stats-change positive">
                                <i class="bi bi-arrow-down"></i> -15.3%
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5><i class="bi bi-graph-up me-2"></i>请求趋势</h5>
                            <div class="chart-controls">
                                <button class="btn btn-sm btn-outline-primary active" data-period="1h">1小时</button>
                                <button class="btn btn-sm btn-outline-primary" data-period="6h">6小时</button>
                                <button class="btn btn-sm btn-outline-primary" data-period="24h">24小时</button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="requestTrendChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5><i class="bi bi-pie-chart me-2"></i>请求类型分布</h5>
                        </div>
                        <div class="chart-body">
                            <canvas id="requestTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时状态 -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="status-card">
                        <div class="status-header">
                            <h5><i class="bi bi-cpu me-2"></i>系统状态</h5>
                            <span class="status-indicator online">在线</span>
                        </div>
                        <div class="status-body">
                            <div class="status-item">
                                <span class="status-label">CPU使用率</span>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" id="cpuUsage" style="width: 0%"></div>
                                </div>
                                <span class="status-value" id="cpuValue">0%</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">内存使用率</span>
                                <div class="progress">
                                    <div class="progress-bar bg-success" id="memoryUsage" style="width: 0%"></div>
                                </div>
                                <span class="status-value" id="memoryValue">0%</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">磁盘使用率</span>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" id="diskUsage" style="width: 0%"></div>
                                </div>
                                <span class="status-value" id="diskValue">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 mb-4">
                    <div class="activity-card">
                        <div class="activity-header">
                            <h5><i class="bi bi-activity me-2"></i>最近活动</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshActivity()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="activity-body" id="recentActivity">
                            <!-- 动态加载最近活动 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他页面内容将通过JavaScript动态加载 -->
        <div id="logs-page" class="page-content">
            <div class="page-header">
                <h2><i class="bi bi-journal-text me-2"></i>实时日志</h2>
                <p class="text-muted">查看系统实时日志和客户访问记录</p>
            </div>
            <!-- 日志内容将通过JavaScript加载 -->
        </div>

        <div id="customers-page" class="page-content">
            <div class="page-header">
                <h2><i class="bi bi-people me-2"></i>客户统计</h2>
                <p class="text-muted">客户使用情况统计和分析</p>
            </div>
            <!-- 客户统计内容将通过JavaScript加载 -->
        </div>

        <div id="security-page" class="page-content">
            <div class="page-header">
                <h2><i class="bi bi-shield-exclamation me-2"></i>安全监控</h2>
                <p class="text-muted">安全威胁检测和异常行为分析</p>
            </div>
            <!-- 安全监控内容将通过JavaScript加载 -->
        </div>

        <div id="analytics-page" class="page-content">
            <div class="page-header">
                <h2><i class="bi bi-graph-up me-2"></i>数据分析</h2>
                <p class="text-muted">深度数据分析和业务洞察</p>
            </div>
            <!-- 数据分析内容将通过JavaScript加载 -->
        </div>

        <div id="settings-page" class="page-content">
            <div class="page-header">
                <h2><i class="bi bi-gear me-2"></i>系统设置</h2>
                <p class="text-muted">系统配置和管理设置</p>
            </div>
            <!-- 系统设置内容将通过JavaScript加载 -->
        </div>
    </div>

    <!-- 系统信息模态框 -->
    <div class="modal fade" id="systemInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>系统信息
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>系统名称</strong></td>
                            <td>星巴克风控绕过系统 - 监控后台</td>
                        </tr>
                        <tr>
                            <td><strong>版本</strong></td>
                            <td>v1.0.0</td>
                        </tr>
                        <tr>
                            <td><strong>运行时间</strong></td>
                            <td id="systemUptime">计算中...</td>
                        </tr>
                        <tr>
                            <td><strong>服务器时间</strong></td>
                            <td id="serverTime">加载中...</td>
                        </tr>
                        <tr>
                            <td><strong>监控端口</strong></td>
                            <td>9000</td>
                        </tr>
                        <tr>
                            <td><strong>数据库状态</strong></td>
                            <td><span class="badge bg-success">正常</span></td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
