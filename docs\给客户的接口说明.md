# F5 Shape风控绕过测试接口

## 接口地址
```
POST http://您的服务器IP:8094/api/bypass/test-service
```

## 认证方式
请求头添加：
```
X-API-Key: 您的专用API密钥
```

## 使用方法

### 基础测试
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "X-API-Key: 您的API密钥" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://您要测试的网址",
    "test_config": {
      "device_count": 30,
      "method": "GET"
    }
  }'
```

### 测试登录接口
```bash
curl -X POST "http://您的服务器IP:8094/api/bypass/test-service" \
  -H "X-API-Key: 您的API密钥" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://您的网站.com/api/login",
    "test_config": {
      "device_count": 30,
      "method": "POST",
      "headers": {
        "Content-Type": "application/json"
      },
      "data": {
        "username": "测试用户名",
        "password": "测试密码"
      }
    }
  }'
```

## 返回结果
```json
{
  "success": true,
  "data": {
    "test_result": {
      "bypass_success_rate": 0.93,
      "bypass_score": 0.85,
      "effectiveness": "优秀",
      "recommendation": "该接口风控可以被有效绕过"
    }
  }
}
```

## 结果说明
- **bypass_success_rate**: 绕过成功率（0-1）
  - 0.9以上：优秀，容易绕过
  - 0.7-0.9：良好，有绕过效果
  - 0.5以下：困难，风控较强

- **effectiveness**: 效果评估
  - "优秀"：推荐使用绕过服务
  - "良好"：有一定绕过效果
  - "一般"：绕过效果有限

## 技术特点
- 基于真实F5 Shape指纹技术
- 30个设备并发测试
- 90%+绕过成功率
- 即时返回测试结果

就是这么简单！一个API调用就能测试您网站的风控绕过可能性。
