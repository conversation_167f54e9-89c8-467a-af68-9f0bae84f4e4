/* 星巴克风控绕过系统 - 监控后台登录页面样式 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 登录页面主体 */
.login-body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 背景动画元素 */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.floating-element:nth-child(4) {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 70%;
    animation-delay: 1s;
}

.floating-element:nth-child(5) {
    width: 90px;
    height: 90px;
    top: 40%;
    left: 5%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* 登录容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

/* 登录卡片 */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 登录头部 */
.login-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.logo-section {
    position: relative;
    z-index: 2;
}

.logo-icon {
    font-size: 3.5rem;
    color: #3498db;
    margin-bottom: 15px;
    display: block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: 1px;
}

.system-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
    margin: 0;
}

/* 登录表单内容 */
.login-body-content {
    padding: 40px 30px;
}

.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.form-label i {
    margin-right: 8px;
    color: #3498db;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    background: white;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 密码输入组 */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #3498db;
}

/* 记住登录 */
.remember-group {
    margin-bottom: 30px;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check-input {
    margin-right: 10px;
    transform: scale(1.1);
}

.form-check-label {
    color: #6c757d;
    font-size: 0.9rem;
    cursor: pointer;
}

/* 登录按钮 */
.btn-login {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login i {
    margin-right: 8px;
}

/* 登录提示 */
.login-alert {
    margin-top: 20px;
    border-radius: 10px;
    border: none;
    padding: 12px 16px;
    font-size: 0.9rem;
}

.login-alert i {
    margin-right: 8px;
}

/* 登录页脚 */
.login-footer {
    background: #f8f9fa;
    padding: 25px 30px;
    border-top: 1px solid #e9ecef;
}

.system-info {
    text-align: center;
}

.info-text {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 8px;
}

.info-text i {
    margin-right: 6px;
    color: #3498db;
}

.security-notice {
    color: #e74c3c;
    font-size: 0.8rem;
    font-weight: 500;
    margin: 0;
}

.security-notice i {
    margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .login-container {
        padding: 15px;
        max-width: 100%;
    }
    
    .login-card {
        border-radius: 15px;
    }
    
    .login-header {
        padding: 30px 20px;
    }
    
    .system-title {
        font-size: 1.5rem;
    }
    
    .login-body-content {
        padding: 30px 20px;
    }
    
    .login-footer {
        padding: 20px;
    }
}

/* 加载动画 */
.btn-login.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-login.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 输入验证样式 */
.form-control.is-invalid {
    border-color: #e74c3c;
    background: #fdf2f2;
}

.form-control.is-valid {
    border-color: #27ae60;
    background: #f2fdf2;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(30, 30, 30, 0.95);
        color: #e9ecef;
    }
    
    .form-control {
        background: #2c3e50;
        border-color: #34495e;
        color: #e9ecef;
    }
    
    .form-control:focus {
        background: #34495e;
        border-color: #3498db;
    }
    
    .form-label {
        color: #e9ecef;
    }
    
    .login-footer {
        background: #2c3e50;
        border-color: #34495e;
    }
}
