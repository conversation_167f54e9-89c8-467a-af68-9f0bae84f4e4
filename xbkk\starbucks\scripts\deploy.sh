#!/bin/bash
# 星巴克设备指纹风控绕过系统部署脚本
# 作者：YINGAshadow
# 创建时间：2025-7-29
# 功能：在Linux Ubuntu服务器上部署系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="starbucks-bypass"
PROJECT_DIR="/opt/${PROJECT_NAME}"
VENV_DIR="${PROJECT_DIR}/venv"
SERVICE_NAME="starbucks-bypass"
PYTHON_VERSION="3.8"

# 获取部署用户信息
DEPLOY_USER="${SUDO_USER:-$(whoami)}"
if [[ "$DEPLOY_USER" == "sbdeploy" ]]; then
    USER_PROJECT_DIR="/home/<USER>/starbucks"
    USER_HOME="/home/<USER>"
else
    USER_PROJECT_DIR="$HOME/starbucks"
    USER_HOME="$HOME"
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查用户权限 - 禁止root直接执行
check_user_permission() {
    # 获取真实用户（即使使用sudo）
    REAL_USER="${SUDO_USER:-$(whoami)}"
    CURRENT_USER=$(whoami)

    log_info "当前执行用户: $CURRENT_USER"
    log_info "真实用户: $REAL_USER"

    # 检查是否为root用户直接执行（不是通过sudo）
    if [[ $EUID -eq 0 && -z "$SUDO_USER" ]]; then
        log_error "安全限制：禁止root用户直接执行部署脚本"
        log_error "请使用专用部署用户执行此脚本"
        echo ""
        echo "正确的部署流程："
        echo "1. 创建部署用户: sudo ./starbucks/scripts/create_deploy_user.sh"
        echo "2. 切换到部署用户: su - sbdeploy"
        echo "3. 执行部署: cd ~/starbucks && sudo ./scripts/deploy.sh"
        echo "4. 删除部署用户: exit && sudo ./starbucks/scripts/delete_deploy_user.sh"
        echo ""
        exit 1
    fi

    # 检查是否为指定的部署用户
    if [[ "$REAL_USER" != "sbdeploy" ]]; then
        log_error "真实用户: $REAL_USER"
        log_error "部署脚本只能由专用部署用户执行: sbdeploy"
        echo ""
        echo "正确的执行流程："
        echo "1. 使用root创建用户: sudo ./starbucks/scripts/create_deploy_user.sh"
        echo "2. 切换到部署用户: su - sbdeploy"
        echo "3. 执行部署: cd ~/starbucks && sudo ./scripts/deploy.sh"
        echo ""
        exit 1
    fi

    # 检查是否有sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_error "当前用户没有sudo权限"
        log_info "请确保用户在sudo组中"
        exit 1
    fi

    log_info "用户权限检查通过: $REAL_USER"
}

# 检查系统版本
check_system() {
    log_step "检查系统环境"
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    source /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]]; then
        log_error "此脚本仅支持Ubuntu系统"
        exit 1
    fi
    
    log_info "系统版本: $PRETTY_NAME"
    
    # 检查Ubuntu版本
    if [[ "$VERSION_ID" < "20.04" ]]; then
        log_warn "建议使用Ubuntu 20.04或更高版本"
    fi
}

# 跳过系统更新，直接安装必要软件
update_system() {
    log_step "跳过系统更新，仅更新包列表"

    sudo apt update

    log_info "包列表更新完成"
}

# 安装依赖包
install_dependencies() {
    log_step "安装系统依赖"

    sudo apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        curl \
        wget \
        git \
        supervisor \
        nginx \
        sqlite3 \
        htop \
        vim \
        unzip
    
    log_info "系统依赖安装完成"
}

# 验证用户存在
verify_user() {
    log_step "验证部署用户"

    if id "$DEPLOY_USER" &>/dev/null; then
        log_info "使用现有部署用户: $DEPLOY_USER"
        log_info "项目将以用户 $DEPLOY_USER 身份运行"
    else
        log_error "部署用户 $DEPLOY_USER 不存在"
        log_error "请先运行 create_deploy_user.sh 创建部署用户"
        exit 1
    fi
}

# 创建项目目录
create_directories() {
    log_step "创建项目目录"
    
    sudo mkdir -p $PROJECT_DIR
    sudo mkdir -p $PROJECT_DIR/logs
    sudo mkdir -p $PROJECT_DIR/data
    sudo mkdir -p $PROJECT_DIR/config
    sudo mkdir -p /var/log/$PROJECT_NAME
    
    log_info "项目目录创建完成"
}

# 复制项目文件
copy_project_files() {
    log_step "复制项目文件"

    log_info "部署用户: $DEPLOY_USER"
    log_info "用户项目目录: $USER_PROJECT_DIR"
    log_info "目标项目目录: $PROJECT_DIR"

    # 从用户项目目录复制文件
    if [[ -d "$USER_PROJECT_DIR" ]]; then
        log_info "找到用户项目目录，开始复制文件..."

        # 显示源目录内容
        log_info "源目录内容:"
        ls -la "$USER_PROJECT_DIR" || true
        # 复制源代码
        if [[ -d "$USER_PROJECT_DIR/src" ]]; then
            cp -r "$USER_PROJECT_DIR/src" "$PROJECT_DIR/"
        fi

        # 复制依赖文件
        if [[ -f "$USER_PROJECT_DIR/requirements.txt" ]]; then
            cp "$USER_PROJECT_DIR/requirements.txt" "$PROJECT_DIR/"
        fi

        # 复制数据文件
        if [[ -f "$USER_PROJECT_DIR/abcd.txt" ]]; then
            cp "$USER_PROJECT_DIR/abcd.txt" "$PROJECT_DIR/"
        fi

        # 复制启动脚本
        if [[ -f "$USER_PROJECT_DIR/run.py" ]]; then
            cp "$USER_PROJECT_DIR/run.py" "$PROJECT_DIR/"
        fi

        # 复制配置文件
        if [[ -f "$USER_PROJECT_DIR/config.ini" ]]; then
            cp "$USER_PROJECT_DIR/config.ini" "$PROJECT_DIR/config/"
        fi

        # 复制环境配置文件
        log_info "检查环境配置文件: $USER_PROJECT_DIR/.env"
        if [[ -f "$USER_PROJECT_DIR/.env" ]]; then
            cp "$USER_PROJECT_DIR/.env" "$PROJECT_DIR/"
            log_info "已复制环境配置文件: .env"
            log_info "验证复制结果:"
            ls -la "$PROJECT_DIR/.env" || true
        else
            log_warn "未找到.env文件路径: $USER_PROJECT_DIR/.env"
            log_warn "当前目录内容:"
            ls -la "$USER_PROJECT_DIR/" || true
            log_warn "将使用默认配置"
        fi

        log_info "从用户目录复制项目文件: $USER_PROJECT_DIR"
    else
        log_error "用户项目目录不存在: $USER_PROJECT_DIR"
        exit 1
    fi

    log_info "项目文件复制完成"
}

# 创建Python虚拟环境
create_virtualenv() {
    log_step "创建Python虚拟环境"
    
    python3 -m venv $VENV_DIR
    
    # 激活虚拟环境
    source $VENV_DIR/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装项目依赖
    pip install -r $PROJECT_DIR/requirements.txt
    
    log_info "Python虚拟环境创建完成"
}

# 配置systemd服务
configure_systemd() {
    log_step "配置systemd服务"
    
    cat > /etc/systemd/system/${SERVICE_NAME}.service << EOF
[Unit]
Description=星巴克设备指纹风控绕过系统
After=network.target

[Service]
Type=simple
User=$DEPLOY_USER
Group=$DEPLOY_USER
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$VENV_DIR/bin
EnvironmentFile=$PROJECT_DIR/.env
ExecStart=$VENV_DIR/bin/python -m src.api.main
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable $SERVICE_NAME
    
    log_info "systemd服务配置完成"
}

# 配置Supervisor（备选方案）
configure_supervisor() {
    log_step "配置Supervisor"
    
    cat > /etc/supervisor/conf.d/${SERVICE_NAME}.conf << EOF
[program:$SERVICE_NAME]
command=$VENV_DIR/bin/python -m src.api.main
directory=$PROJECT_DIR
user=$DEPLOY_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/$PROJECT_NAME/supervisor.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PATH="$VENV_DIR/bin"
EOF
    
    supervisorctl reread
    supervisorctl update
    
    log_info "Supervisor配置完成"
}

# 配置Nginx反向代理
configure_nginx() {
    log_step "配置Nginx反向代理"
    
    cat > /etc/nginx/sites-available/$SERVICE_NAME << EOF
server {
    listen 8094;
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:8888;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    location /docs {
        proxy_pass http://127.0.0.1:8888/docs;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location /redoc {
        proxy_pass http://127.0.0.1:8888/redoc;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/

    # 删除默认站点
    sudo rm -f /etc/nginx/sites-enabled/default

    # 测试配置
    sudo nginx -t

    # 重启Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log_info "Nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙"

    # 启用UFW防火墙
    sudo ufw --force enable

    # 开放SSH端口28262
    sudo ufw allow 28262/tcp comment "SSH访问"

    # 开放Nginx端口8094
    sudo ufw allow 8094/tcp comment "星巴克绕过系统Web接口"

    # 开放内部API端口8888（仅本地访问）
    sudo ufw allow from 127.0.0.1 to any port 8888 comment "内部API端口"

    # 显示防火墙状态
    sudo ufw status numbered

    log_info "防火墙配置完成"
    log_info "已开放端口: SSH(28262), Web(8094)"
}

# 设置文件权限
set_permissions() {
    log_step "设置文件权限"
    
    sudo chown -R $DEPLOY_USER:$DEPLOY_USER $PROJECT_DIR
    sudo chown -R $DEPLOY_USER:$DEPLOY_USER /var/log/$PROJECT_NAME

    sudo chmod +x $PROJECT_DIR/src/api/main.py
    sudo chmod 755 $PROJECT_DIR
    sudo chmod 644 $PROJECT_DIR/requirements.txt
    sudo chmod 644 $PROJECT_DIR/abcd.txt
    
    log_info "文件权限设置完成"
}

# 启动服务
start_services() {
    log_step "启动服务"
    
    # 启动主服务
    sudo systemctl start $SERVICE_NAME
    sudo systemctl enable $SERVICE_NAME

    # 启动Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx

    # 检查服务状态
    sleep 3
    if sudo systemctl is-active --quiet $SERVICE_NAME; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        sudo systemctl status $SERVICE_NAME
        exit 1
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本"
    
    # 创建启动脚本
    cat > $PROJECT_DIR/start.sh << 'EOF'
#!/bin/bash
echo "启动星巴克设备指纹风控绕过系统..."
sudo systemctl start starbucks-fingerprint
sudo systemctl status starbucks-fingerprint
EOF
    
    # 创建停止脚本
    cat > $PROJECT_DIR/stop.sh << 'EOF'
#!/bin/bash
echo "停止星巴克设备指纹风控绕过系统..."
sudo systemctl stop starbucks-fingerprint
echo "服务已停止"
EOF
    
    # 创建重启脚本
    cat > $PROJECT_DIR/restart.sh << 'EOF'
#!/bin/bash
echo "重启星巴克设备指纹风控绕过系统..."
sudo systemctl restart starbucks-fingerprint
sudo systemctl status starbucks-fingerprint
EOF
    
    # 创建状态检查脚本
    cat > $PROJECT_DIR/status.sh << 'EOF'
#!/bin/bash
echo "检查星巴克设备指纹风控绕过系统状态..."
sudo systemctl status starbucks-fingerprint
echo ""
echo "端口监听状态:"
sudo netstat -tlnp | grep :8888
echo ""
echo "进程状态:"
ps aux | grep python | grep starbucks
EOF
    
    # 设置执行权限
    sudo chmod +x $PROJECT_DIR/*.sh
    
    log_info "管理脚本创建完成"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成"
    
    echo ""
    echo "=========================================="
    echo "星巴克设备指纹风控绕过系统部署完成"
    echo "=========================================="
    echo ""
    echo "项目目录: $PROJECT_DIR"
    echo "虚拟环境: $VENV_DIR"
    echo "服务名称: $SERVICE_NAME"
    echo "运行用户: $DEPLOY_USER"
    echo ""
    echo "访问地址:"
    echo "  API文档: http://服务器IP:8094/docs"
    echo "  ReDoc文档: http://服务器IP:8094/redoc"
    echo "  健康检查: http://服务器IP:8094/health"
    echo ""
    echo "端口配置:"
    echo "  SSH端口: 28262"
    echo "  Web端口: 8094 (Nginx反向代理)"
    echo "  内部API: 8888 (仅本地访问)"
    echo ""
    echo "管理命令:"
    echo "  启动服务: sudo systemctl start $SERVICE_NAME"
    echo "  停止服务: sudo systemctl stop $SERVICE_NAME"
    echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
    echo "  查看状态: sudo systemctl status $SERVICE_NAME"
    echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "快捷脚本:"
    echo "  启动: $PROJECT_DIR/start.sh"
    echo "  停止: $PROJECT_DIR/stop.sh"
    echo "  重启: $PROJECT_DIR/restart.sh"
    echo "  状态: $PROJECT_DIR/status.sh"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始部署星巴克设备指纹风控绕过系统"

    check_user_permission
    check_system
    update_system
    install_dependencies
    verify_user
    create_directories
    copy_project_files
    create_virtualenv
    configure_systemd
    configure_nginx
    configure_firewall
    set_permissions
    start_services
    create_management_scripts
    show_deployment_info

    log_info "部署完成！"
}

# 执行主函数
main "$@"
