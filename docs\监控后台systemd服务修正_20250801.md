# 监控后台systemd服务修正

**时间**: 2025-8-1  
**问题**: systemd服务启动失败，错误代码203/EXEC  
**原因**: ExecStart配置错误，应该使用uvicorn启动FastAPI应用  

## 🔧 问题分析

### 错误现象
```
● monitor-backend.service - 星巴克风控绕过系统 - 独立监控后台
     Loaded: loaded (/etc/systemd/system/monitor-backend.service; enabled; preset: enabled)
     Active: activating (auto-restart) (Result: exit-code) since Fri 2025-08-01 12:10:01 UTC; 1s ago
    Process: 1882493 ExecStart=/home/<USER>/monitor_backend/venv/bin/python /home/<USER>/monitor_backend/src/monitor_app.py (code=exited, status=203/EXEC)
```

### 错误原因
- **错误配置**: `ExecStart=/home/<USER>/monitor_backend/venv/bin/python /home/<USER>/monitor_backend/src/monitor_app.py`
- **正确配置**: `ExecStart=/home/<USER>/monitor_backend/venv/bin/uvicorn src.monitor_app:app --host 0.0.0.0 --port 9000`

FastAPI应用应该使用uvicorn ASGI服务器启动，而不是直接运行Python文件。

## 🚀 快速修正

### 方法1: 使用修正脚本 (推荐)
```bash
cd ~/monitor_backend
sudo ./fix_systemd_service.sh
```

### 方法2: 手动修正
```bash
# 1. 停止服务
sudo systemctl stop monitor-backend

# 2. 编辑服务文件
sudo nano /etc/systemd/system/monitor-backend.service

# 3. 修改ExecStart行为:
ExecStart=/home/<USER>/monitor_backend/venv/bin/uvicorn src.monitor_app:app --host 0.0.0.0 --port 9000

# 4. 重载配置并启动
sudo systemctl daemon-reload
sudo systemctl start monitor-backend
```

## 📋 完整的正确配置

### systemd服务文件
```ini
[Unit]
Description=星巴克风控绕过系统 - 独立监控后台
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/home/<USER>/monitor_backend
Environment=PATH=/home/<USER>/monitor_backend/venv/bin
ExecStart=/home/<USER>/monitor_backend/venv/bin/uvicorn src.monitor_app:app --host 0.0.0.0 --port 9000
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=monitor-backend

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/monitor_backend

[Install]
WantedBy=multi-user.target
```

### uvicorn启动参数说明
- `src.monitor_app:app` - 指定FastAPI应用对象
- `--host 0.0.0.0` - 监听所有网络接口
- `--port 9000` - 监听端口9000

## ✅ 验证修正结果

### 1. 检查服务状态
```bash
sudo systemctl status monitor-backend
```

预期输出:
```
● monitor-backend.service - 星巴克风控绕过系统 - 独立监控后台
     Loaded: loaded (/etc/systemd/system/monitor-backend.service; enabled; preset: enabled)
     Active: active (running) since Fri 2025-08-01 12:15:01 UTC; 30s ago
   Main PID: 1882600 (uvicorn)
      Tasks: 1 (limit: 4915)
     Memory: 45.2M
        CPU: 1.234s
```

### 2. 测试API响应
```bash
curl http://localhost:9000/health
```

预期输出:
```json
{"status":"healthy","timestamp":"2025-08-01T12:15:30Z"}
```

### 3. 测试Web界面
```bash
curl http://localhost:9000/login | grep "星巴克风控绕过系统"
```

### 4. 测试Nginx代理
```bash
curl http://localhost:9094/health
```

## 🔍 故障排除

### 查看详细日志
```bash
# 查看服务日志
sudo journalctl -u monitor-backend -f

# 查看应用日志
tail -f ~/monitor_backend/logs/monitor_backend.log
```

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep 9000
   sudo lsof -i :9000
   ```

2. **权限问题**
   ```bash
   sudo chown -R monitor:monitor /home/<USER>/monitor_backend
   ```

3. **虚拟环境问题**
   ```bash
   cd ~/monitor_backend
   ./venv/bin/pip list | grep uvicorn
   ```

4. **环境变量问题**
   ```bash
   cat ~/monitor_backend/.env
   ```

## 🎯 手动启动测试

如果systemd服务仍有问题，可以手动启动测试：

```bash
# 切换到monitor用户
su - monitor

# 进入项目目录
cd ~/monitor_backend

# 手动启动
./venv/bin/uvicorn src.monitor_app:app --host 0.0.0.0 --port 9000

# 或者使用Python直接启动
./venv/bin/python src/monitor_app.py
```

## 📊 修正后的访问方式

### Web界面访问
- **外部访问**: `http://服务器IP:9094/login`
- **内部访问**: `http://localhost:9000/login`

### 登录信息
- **管理员账户**: `monitor_admin`
- **登录密码**: `MonitorAdmin2025#Backend!`

### 服务管理
```bash
# 启动服务
sudo systemctl start monitor-backend

# 停止服务
sudo systemctl stop monitor-backend

# 重启服务
sudo systemctl restart monitor-backend

# 查看状态
sudo systemctl status monitor-backend

# 查看日志
sudo journalctl -u monitor-backend -f
```

## 📝 总结

### ✅ 修正内容
1. **ExecStart配置** - 从直接运行Python改为使用uvicorn
2. **启动参数** - 添加正确的host和port参数
3. **工作目录** - 确保在正确的项目目录下运行
4. **环境变量** - 设置正确的PATH环境变量

### 🎯 修正结果
- ✅ systemd服务正常启动
- ✅ Web界面可以访问
- ✅ API接口响应正常
- ✅ Nginx代理工作正常

**现在监控后台systemd服务应该能正常启动了！**
